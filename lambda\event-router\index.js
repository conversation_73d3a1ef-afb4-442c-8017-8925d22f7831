const AWS = require('aws-sdk');
const xml2js = require('xml2js');

const sqs = new AWS.SQS();

// SQS Queue URLs from environment variables
const CALL_EVENTS_QUEUE = process.env.CALL_EVENTS_QUEUE_URL;
const AGENT_EVENTS_QUEUE = process.env.AGENT_EVENTS_QUEUE_URL;
const QUEUE_EVENTS_QUEUE = process.env.QUEUE_EVENTS_QUEUE_URL;
const DLQ_URL = process.env.DLQ_URL;

// Event type mappings
const CALL_EVENT_TYPES = [
    'StartCall', 'Route', 'Answer', 'EndCall', 'EndMedia', 
    'CDRtype1', 'OutboundCall', 'TransferCall', 'Media', 'Hold', 'HoldRetrieved'
];

const AGENT_EVENT_TYPES = [
    'Login', 'Logout', 'Available', 'BusiedOut', 
    'ACDLogin', 'ACDLogout'
];

const QUEUE_EVENT_TYPES = ['QueueStateChange'];

exports.handler = async (event) => {
    console.log('Event Router received:', JSON.stringify(event, null, 2));
    
    const results = [];
    
    for (const record of event.Records) {
        try {
            // Parse SNS message
            const snsMessage = JSON.parse(record.Sns.Message);
            const s3Event = JSON.parse(snsMessage.Message || snsMessage);
            
            // Extract S3 object information
            const bucket = s3Event.Records[0].s3.bucket.name;
            const key = decodeURIComponent(s3Event.Records[0].s3.object.key);
            
            // Download and parse XML from S3
            const xmlContent = await downloadFromS3(bucket, key);
            const parsedEvent = await parseXmlEvent(xmlContent);
            
            if (!parsedEvent) {
                await sendToDLQ(record, 'Failed to parse XML event');
                continue;
            }
            
            // Extract tenant information
            const tenantId = extractTenantId(parsedEvent);
            
            // Route event based on type
            const routingResult = await routeEvent(parsedEvent, tenantId, xmlContent);
            results.push(routingResult);
            
        } catch (error) {
            console.error('Error processing record:', error);
            await sendToDLQ(record, error.message);
            results.push({ success: false, error: error.message });
        }
    }
    
    return {
        statusCode: 200,
        body: JSON.stringify({
            processed: results.length,
            results: results
        })
    };
};

async function downloadFromS3(bucket, key) {
    const s3 = new AWS.S3();
    const params = { Bucket: bucket, Key: key };
    
    try {
        const data = await s3.getObject(params).promise();
        return data.Body.toString('utf-8');
    } catch (error) {
        console.error('Error downloading from S3:', error);
        throw error;
    }
}

async function parseXmlEvent(xmlContent) {
    try {
        const parser = new xml2js.Parser({ 
            explicitArray: false,
            ignoreAttrs: false,
            mergeAttrs: true
        });
        
        const result = await parser.parseStringPromise(xmlContent);
        return result.LogEvent || result.LogEvents?.LogEvent;
    } catch (error) {
        console.error('Error parsing XML:', error);
        return null;
    }
}

function extractTenantId(parsedEvent) {
    // Extract tenant from agencyOrElement field
    // Format: "tng000_A" -> tenant_id = "tng000"
    const agencyOrElement = parsedEvent.agencyOrElement;
    if (!agencyOrElement) return null;
    
    // Remove suffix after underscore
    const tenantId = agencyOrElement.split('_')[0];
    return tenantId;
}

async function routeEvent(parsedEvent, tenantId, xmlContent) {
    const eventType = parsedEvent.eventType;
    
    if (!eventType) {
        throw new Error('Missing eventType in parsed event');
    }
    
    let queueUrl;
    
    if (CALL_EVENT_TYPES.includes(eventType)) {
        queueUrl = CALL_EVENTS_QUEUE;
    } else if (AGENT_EVENT_TYPES.includes(eventType)) {
        queueUrl = AGENT_EVENTS_QUEUE;
    } else if (QUEUE_EVENT_TYPES.includes(eventType)) {
        queueUrl = QUEUE_EVENTS_QUEUE;
    } else {
        throw new Error(`Unknown event type: ${eventType}`);
    }
    
    // Prepare message with enriched metadata
    const message = {
        tenantId: tenantId,
        eventType: eventType,
        callIdentifier: parsedEvent.callIdentifier,
        timestamp: parsedEvent.timestamp,
        agent: parsedEvent.agent,
        agencyOrElement: parsedEvent.agencyOrElement,
        xmlContent: xmlContent,
        parsedEvent: parsedEvent,
        receivedAt: new Date().toISOString()
    };
    
    const params = {
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify(message),
        MessageAttributes: {
            tenantId: {
                DataType: 'String',
                StringValue: tenantId || 'unknown'
            },
            eventType: {
                DataType: 'String',
                StringValue: eventType
            },
            callIdentifier: {
                DataType: 'String',
                StringValue: parsedEvent.callIdentifier || ''
            }
        }
    };
    
    try {
        const result = await sqs.sendMessage(params).promise();
        console.log(`Routed ${eventType} event to ${queueUrl}:`, result.MessageId);
        return { success: true, messageId: result.MessageId, queueUrl };
    } catch (error) {
        console.error('Error sending to SQS:', error);
        throw error;
    }
}

async function sendToDLQ(record, errorMessage) {
    const params = {
        QueueUrl: DLQ_URL,
        MessageBody: JSON.stringify({
            originalRecord: record,
            error: errorMessage,
            timestamp: new Date().toISOString()
        })
    };
    
    try {
        await sqs.sendMessage(params).promise();
        console.log('Sent failed record to DLQ');
    } catch (error) {
        console.error('Error sending to DLQ:', error);
    }
}
