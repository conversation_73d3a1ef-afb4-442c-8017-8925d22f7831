-- =====================================================
-- Redshift Data Warehouse Schema - Fact Tables
-- =====================================================

-- =====================================================
-- FACT TABLES
-- =====================================================

-- Call Fact Table
CREATE TABLE IF NOT EXISTS reporting.fact_call (
    call_fact_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    agent_id INTEGER,
    queue_id INTEGER,
    call_type_id INTEGER NOT NULL,
    start_time_id INTEGER NOT NULL,
    end_time_id INTEGER,
    call_identifier VARCHAR(256) NOT NULL,
    talk_time_sec INTEGER,
    total_call_sec INTEGER,
    abandoned_flag BOOLEAN DEFAULT FALSE,
    transferred_in_flag BOOLEAN DEFAULT FALSE,
    transferred_out_flag BOOLEAN DEFAULT FALSE,
    transfer_from_queue VARCHAR(255),
    transfer_to_queue VARCHAR(255),
    answered_le_10s_flag BOOLEAN DEFAULT FALSE,
    answered_le_15s_flag BOOLEAN DEFAULT FALSE,
    answered_le_20s_flag BOOLEAN DEFAULT FALSE,
    answered_le_40s_flag BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT GETDATE(),
    FOREIGN KEY (tenant_id) REFERENCES reporting.dim_tenant(tenant_id),
    FOREIGN KEY (agent_id) REFERENCES reporting.dim_agent(agent_id),
    FOREIGN KEY (queue_id) REFERENCES reporting.dim_queue(queue_id),
    FOREIGN KEY (call_type_id) REFERENCES reporting.dim_call_type(call_type_id),
    FOREIGN KEY (start_time_id) REFERENCES reporting.dim_time(time_id),
    FOREIGN KEY (end_time_id) REFERENCES reporting.dim_time(time_id)
)
DISTSTYLE KEY
DISTKEY (tenant_id)
SORTKEY (tenant_id, start_time_id, call_identifier);

-- Agent Session Fact Table
CREATE TABLE IF NOT EXISTS reporting.fact_agent_session (
    session_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    agent_id INTEGER NOT NULL,
    queue_id INTEGER,
    login_time_id INTEGER NOT NULL,
    logout_time_id INTEGER,
    login_timestamp TIMESTAMP NOT NULL,
    logout_timestamp TIMESTAMP,
    session_duration_sec INTEGER,
    device_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT GETDATE(),
    FOREIGN KEY (tenant_id) REFERENCES reporting.dim_tenant(tenant_id),
    FOREIGN KEY (agent_id) REFERENCES reporting.dim_agent(agent_id),
    FOREIGN KEY (queue_id) REFERENCES reporting.dim_queue(queue_id),
    FOREIGN KEY (login_time_id) REFERENCES reporting.dim_time(time_id),
    FOREIGN KEY (logout_time_id) REFERENCES reporting.dim_time(time_id)
)
DISTSTYLE KEY
DISTKEY (tenant_id)
SORTKEY (tenant_id, agent_id, login_time_id);

-- Agent State Fact Table
CREATE TABLE IF NOT EXISTS reporting.fact_agent_state (
    state_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    agent_id INTEGER NOT NULL,
    queue_id INTEGER,
    reason_id INTEGER,
    state_start_time_id INTEGER NOT NULL,
    state_end_time_id INTEGER,
    state_start_time TIMESTAMP NOT NULL,
    state_end_time TIMESTAMP,
    state_duration_sec INTEGER,
    state_type VARCHAR(50) NOT NULL, -- LOGGED_IN/AVAILABLE/BUSY_WRAPUP/BUSY_OTHER/ONCALL/LOGGED_OUT
    busied_out_action VARCHAR(100), -- WrapUpFixedDuration/WrapUpVariableDuration/Break/Training/Manual
    created_at TIMESTAMP DEFAULT GETDATE(),
    FOREIGN KEY (tenant_id) REFERENCES reporting.dim_tenant(tenant_id),
    FOREIGN KEY (agent_id) REFERENCES reporting.dim_agent(agent_id),
    FOREIGN KEY (queue_id) REFERENCES reporting.dim_queue(queue_id),
    FOREIGN KEY (reason_id) REFERENCES reporting.dim_reason_code(reason_id),
    FOREIGN KEY (state_start_time_id) REFERENCES reporting.dim_time(time_id),
    FOREIGN KEY (state_end_time_id) REFERENCES reporting.dim_time(time_id)
)
DISTSTYLE KEY
DISTKEY (tenant_id)
SORTKEY (tenant_id, agent_id, state_start_time_id);

-- Agent Intervals Fact Table (5-minute aggregations)
CREATE TABLE IF NOT EXISTS reporting.fact_agent_intervals (
    agent_interval_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    agent_id INTEGER NOT NULL,
    queue_id INTEGER,
    time_id INTEGER NOT NULL,
    interval_start TIMESTAMP NOT NULL,
    interval_end TIMESTAMP NOT NULL,
    staffed_time_seconds INTEGER DEFAULT 0,
    available_time_seconds INTEGER DEFAULT 0,
    wrapup_time_seconds INTEGER DEFAULT 0,
    busy_other_time_seconds INTEGER DEFAULT 0,
    talk_time_seconds INTEGER DEFAULT 0,
    calls_answered INTEGER DEFAULT 0,
    transferred_in INTEGER DEFAULT 0,
    transferred_out INTEGER DEFAULT 0,
    agents_logged_in_seconds INTEGER DEFAULT 0,
    service_level_10s DECIMAL(5,4) DEFAULT 0,
    service_level_15s DECIMAL(5,4) DEFAULT 0,
    service_level_20s DECIMAL(5,4) DEFAULT 0,
    service_level_40s DECIMAL(5,4) DEFAULT 0,
    created_at TIMESTAMP DEFAULT GETDATE(),
    FOREIGN KEY (tenant_id) REFERENCES reporting.dim_tenant(tenant_id),
    FOREIGN KEY (agent_id) REFERENCES reporting.dim_agent(agent_id),
    FOREIGN KEY (queue_id) REFERENCES reporting.dim_queue(queue_id),
    FOREIGN KEY (time_id) REFERENCES reporting.dim_time(time_id),
    UNIQUE (tenant_id, agent_id, queue_id, time_id)
)
DISTSTYLE KEY
DISTKEY (tenant_id)
SORTKEY (tenant_id, time_id, agent_id);

-- =====================================================
-- FACT TABLE PROCEDURES
-- =====================================================

-- Procedure to insert call fact
CREATE OR REPLACE PROCEDURE reporting.insert_call_fact(
    p_tenant_id INTEGER,
    p_agent_id INTEGER,
    p_queue_id INTEGER,
    p_call_type_id INTEGER,
    p_call_identifier VARCHAR(256),
    p_start_timestamp TIMESTAMP,
    p_end_timestamp TIMESTAMP,
    p_talk_time_sec INTEGER,
    p_total_call_sec INTEGER,
    p_abandoned_flag BOOLEAN,
    p_transferred_in_flag BOOLEAN,
    p_transferred_out_flag BOOLEAN,
    p_transfer_from_queue VARCHAR(255),
    p_transfer_to_queue VARCHAR(255),
    p_answered_le_10s_flag BOOLEAN,
    p_answered_le_15s_flag BOOLEAN,
    p_answered_le_20s_flag BOOLEAN,
    p_answered_le_40s_flag BOOLEAN
)
AS $$
DECLARE
    v_start_time_id INTEGER;
    v_end_time_id INTEGER;
BEGIN
    -- Get time dimension IDs
    SELECT time_id INTO v_start_time_id
    FROM reporting.dim_time
    WHERE full_date = p_start_timestamp::DATE
      AND hour = EXTRACT(HOUR FROM p_start_timestamp)
      AND minute = EXTRACT(MINUTE FROM p_start_timestamp);
    
    IF p_end_timestamp IS NOT NULL THEN
        SELECT time_id INTO v_end_time_id
        FROM reporting.dim_time
        WHERE full_date = p_end_timestamp::DATE
          AND hour = EXTRACT(HOUR FROM p_end_timestamp)
          AND minute = EXTRACT(MINUTE FROM p_end_timestamp);
    END IF;
    
    -- Insert call fact
    INSERT INTO reporting.fact_call (
        tenant_id, agent_id, queue_id, call_type_id, start_time_id, end_time_id,
        call_identifier, talk_time_sec, total_call_sec, abandoned_flag,
        transferred_in_flag, transferred_out_flag, transfer_from_queue, transfer_to_queue,
        answered_le_10s_flag, answered_le_15s_flag, answered_le_20s_flag, answered_le_40s_flag
    ) VALUES (
        p_tenant_id, p_agent_id, p_queue_id, p_call_type_id, v_start_time_id, v_end_time_id,
        p_call_identifier, p_talk_time_sec, p_total_call_sec, p_abandoned_flag,
        p_transferred_in_flag, p_transferred_out_flag, p_transfer_from_queue, p_transfer_to_queue,
        p_answered_le_10s_flag, p_answered_le_15s_flag, p_answered_le_20s_flag, p_answered_le_40s_flag
    );
END;
$$ LANGUAGE plpgsql;

-- Procedure to update agent intervals
CREATE OR REPLACE PROCEDURE reporting.update_agent_intervals(
    p_tenant_id INTEGER,
    p_agent_id INTEGER,
    p_queue_id INTEGER,
    p_interval_start TIMESTAMP,
    p_staffed_time_seconds INTEGER DEFAULT 0,
    p_available_time_seconds INTEGER DEFAULT 0,
    p_wrapup_time_seconds INTEGER DEFAULT 0,
    p_busy_other_time_seconds INTEGER DEFAULT 0,
    p_talk_time_seconds INTEGER DEFAULT 0,
    p_calls_answered INTEGER DEFAULT 0,
    p_transferred_in INTEGER DEFAULT 0,
    p_transferred_out INTEGER DEFAULT 0
)
AS $$
DECLARE
    v_time_id INTEGER;
    v_interval_end TIMESTAMP;
    v_existing_id BIGINT;
BEGIN
    -- Calculate 5-minute interval end
    v_interval_end := p_interval_start + INTERVAL '5 minutes';

    -- Get time dimension ID for the interval
    SELECT time_id INTO v_time_id
    FROM reporting.dim_time
    WHERE full_date = p_interval_start::DATE
      AND hour = EXTRACT(HOUR FROM p_interval_start)
      AND minute = (EXTRACT(MINUTE FROM p_interval_start) / 5) * 5;

    -- Check if record exists
    SELECT agent_interval_id INTO v_existing_id
    FROM reporting.fact_agent_intervals
    WHERE tenant_id = p_tenant_id
      AND agent_id = p_agent_id
      AND COALESCE(queue_id, -1) = COALESCE(p_queue_id, -1)
      AND time_id = v_time_id;

    IF v_existing_id IS NULL THEN
        -- Insert new interval record
        INSERT INTO reporting.fact_agent_intervals (
            tenant_id, agent_id, queue_id, time_id, interval_start, interval_end,
            staffed_time_seconds, available_time_seconds, wrapup_time_seconds,
            busy_other_time_seconds, talk_time_seconds, calls_answered,
            transferred_in, transferred_out, agents_logged_in_seconds
        ) VALUES (
            p_tenant_id, p_agent_id, p_queue_id, v_time_id, p_interval_start, v_interval_end,
            p_staffed_time_seconds, p_available_time_seconds, p_wrapup_time_seconds,
            p_busy_other_time_seconds, p_talk_time_seconds, p_calls_answered,
            p_transferred_in, p_transferred_out,
            CASE WHEN p_staffed_time_seconds > 0 THEN 300 ELSE 0 END
        );
    ELSE
        -- Update existing record
        UPDATE reporting.fact_agent_intervals
        SET staffed_time_seconds = staffed_time_seconds + p_staffed_time_seconds,
            available_time_seconds = available_time_seconds + p_available_time_seconds,
            wrapup_time_seconds = wrapup_time_seconds + p_wrapup_time_seconds,
            busy_other_time_seconds = busy_other_time_seconds + p_busy_other_time_seconds,
            talk_time_seconds = talk_time_seconds + p_talk_time_seconds,
            calls_answered = calls_answered + p_calls_answered,
            transferred_in = transferred_in + p_transferred_in,
            transferred_out = transferred_out + p_transferred_out,
            agents_logged_in_seconds = CASE
                WHEN p_staffed_time_seconds > 0 THEN 300
                ELSE agents_logged_in_seconds
            END
        WHERE agent_interval_id = v_existing_id;
    END IF;
END;
$$ LANGUAGE plpgsql;
