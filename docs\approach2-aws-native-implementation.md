# Approach 2: Full AWS Native Implementation

## Architecture Overview

```mermaid
graph TB
    subgraph "Event Sources"
        S3[S3 i3 XML Logs<br/>brandon911-bucket]
        SNS[SNS Topic<br/>i3-events-brandon911]
    end
    
    subgraph "SNS Filtering"
        CF[Call Filter<br/>StartCall,EndCall,CDRType1]
        AF[Agent Filter<br/>Login,Logout,Available]
        QF[Queue Filter<br/>QueueStateChange]
    end
    
    subgraph "Call Processing (AWS Native)"
        CL[brandon911-call-processor<br/>Lambda]
        AC[(DynamoDB<br/>brandon911-active-calls)]
        CSF[Call Completion<br/>Step Functions]
    end
    
    subgraph "Agent Processing (AWS Native)"
        AL[brandon911-agent-processor<br/>Lambda]
        AS[(DynamoDB<br/>brandon911-agent-sessions)]
        ASF[Agent Session<br/>Step Functions]
    end
    
    subgraph "Queue Processing (AWS Native)"
        QL[brandon911-queue-processor<br/>Lambda]
        QS[(DynamoDB<br/>brandon911-queue-states)]
    end
    
    subgraph "Data Warehouse"
        RS[(Redshift<br/>brandon911_reporting)]
        PBI[Power BI<br/>Real-time Refresh]
    end
    
    S3 --> SNS
    SNS --> CF
    SNS --> AF
    SNS --> QF
    
    CF --> CL
    CL --> AC
    CL --> CSF
    CSF --> RS
    
    AF --> AL
    AL --> AS
    AL --> ASF
    ASF --> RS
    
    QF --> QL
    QL --> QS
    QL --> RS
    
    RS --> PBI
```

## Call Processing Lambda (AWS Native)

### brandon911-call-processor Lambda
```javascript
const AWS = require('aws-sdk');

const dynamodb = new AWS.DynamoDB.DocumentClient();
const stepfunctions = new AWS.StepFunctions();

const TENANT_ID = process.env.TENANT_ID;
const ACTIVE_CALLS_TABLE = `${TENANT_ID}-active-calls`;
const CALL_COMPLETION_STATE_MACHINE = process.env.CALL_COMPLETION_STATE_MACHINE_ARN;

exports.handler = async (event) => {
    console.log(`Processing call events for tenant: ${TENANT_ID}`);
    
    const results = [];
    
    for (const record of event.Records) {
        try {
            const snsMessage = JSON.parse(record.Sns.Message);
            const result = await processCallEvent(snsMessage);
            results.push(result);
        } catch (error) {
            console.error('Error processing call event:', error);
            results.push({ success: false, error: error.message });
        }
    }
    
    return {
        statusCode: 200,
        body: JSON.stringify({
            tenant: TENANT_ID,
            processed: results.length,
            results: results
        })
    };
};

async function processCallEvent(snsMessage) {
    const { eventType, parsedEvent, timestamp } = snsMessage;
    const callIdentifier = parsedEvent.callIdentifier;
    
    console.log(`Processing ${eventType} for call ${callIdentifier}, tenant ${TENANT_ID}`);
    
    // Get or create call record
    let callRecord = await getCallRecord(callIdentifier);
    
    if (!callRecord) {
        callRecord = await createCallRecord(callIdentifier, parsedEvent, timestamp);
    }
    
    // Add event to call timeline
    await addEventToCall(callRecord, eventType, parsedEvent, timestamp);
    
    // Check if call is complete based on your ER diagram logic
    const isComplete = await checkCallCompletion(callRecord, eventType);
    
    if (isComplete) {
        console.log(`Call ${callIdentifier} is complete, triggering Step Function`);
        await triggerCallCompletion(callRecord);
    }
    
    return { 
        success: true, 
        callIdentifier, 
        eventType, 
        isComplete,
        eventCount: callRecord.events ? callRecord.events.length + 1 : 1
    };
}

async function getCallRecord(callIdentifier) {
    try {
        const result = await dynamodb.get({
            TableName: ACTIVE_CALLS_TABLE,
            Key: {
                tenantId: TENANT_ID,
                callIdentifier: callIdentifier
            }
        }).promise();
        
        return result.Item;
    } catch (error) {
        console.error('Error getting call record:', error);
        return null;
    }
}

async function createCallRecord(callIdentifier, parsedEvent, timestamp) {
    const callRecord = {
        tenantId: TENANT_ID,
        callIdentifier: callIdentifier,
        status: 'ACTIVE',
        events: [],
        startTime: timestamp,
        agencyOrElement: parsedEvent.agencyOrElement,
        agent: parsedEvent.agent,
        queue: parsedEvent.queue,
        createdAt: new Date().toISOString(),
        ttl: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours TTL
        completionFlags: {
            hasStartCall: false,
            hasEndCall: false,
            hasEndMedia: false,
            hasCDRType1: false,
            hasOutboundCall: false,
            hasAnswer: false
        }
    };
    
    await dynamodb.put({
        TableName: ACTIVE_CALLS_TABLE,
        Item: callRecord
    }).promise();
    
    return callRecord;
}

async function addEventToCall(callRecord, eventType, parsedEvent, timestamp) {
    const eventData = {
        eventType: eventType,
        timestamp: timestamp,
        data: parsedEvent,
        receivedAt: new Date().toISOString()
    };
    
    // Update completion flags based on event type
    const updateExpression = [];
    const expressionAttributeValues = {};
    const expressionAttributeNames = {};
    
    updateExpression.push('events = list_append(if_not_exists(events, :empty_list), :new_event)');
    expressionAttributeValues[':empty_list'] = [];
    expressionAttributeValues[':new_event'] = [eventData];
    
    // Update completion flags based on your ER diagram requirements
    switch (eventType) {
        case 'StartCall':
            updateExpression.push('completionFlags.hasStartCall = :true');
            expressionAttributeValues[':true'] = true;
            break;
        case 'EndCall':
            updateExpression.push('completionFlags.hasEndCall = :true');
            break;
        case 'EndMedia':
            updateExpression.push('completionFlags.hasEndMedia = :true');
            break;
        case 'CDRtype1':
            updateExpression.push('completionFlags.hasCDRType1 = :true');
            break;
        case 'OutboundCall':
            updateExpression.push('completionFlags.hasOutboundCall = :true');
            break;
        case 'Answer':
            updateExpression.push('completionFlags.hasAnswer = :true');
            break;
    }
    
    await dynamodb.update({
        TableName: ACTIVE_CALLS_TABLE,
        Key: {
            tenantId: TENANT_ID,
            callIdentifier: callRecord.callIdentifier
        },
        UpdateExpression: 'SET ' + updateExpression.join(', '),
        ExpressionAttributeValues: expressionAttributeValues,
        ExpressionAttributeNames: expressionAttributeNames
    }).promise();
}

async function checkCallCompletion(callRecord, latestEventType) {
    // Get updated completion flags
    const updatedRecord = await getCallRecord(callRecord.callIdentifier);
    const flags = updatedRecord.completionFlags;
    
    // Call completion logic based on your ER diagram:
    // 1. Must have EndCall OR EndMedia
    // 2. For non-outbound calls, must also have CDRType1
    // 3. Emergency calls require CDRType1
    
    const hasEndEvent = flags.hasEndCall || flags.hasEndMedia;
    
    if (!hasEndEvent) {
        return false;
    }
    
    // Outbound calls don't require CDRType1
    if (flags.hasOutboundCall) {
        return true;
    }
    
    // Emergency/Admin calls require CDRType1
    return flags.hasCDRType1;
}

async function triggerCallCompletion(callRecord) {
    const input = {
        tenantId: TENANT_ID,
        callIdentifier: callRecord.callIdentifier,
        completedAt: new Date().toISOString()
    };
    
    try {
        const result = await stepfunctions.startExecution({
            stateMachineArn: CALL_COMPLETION_STATE_MACHINE,
            input: JSON.stringify(input),
            name: `call-completion-${TENANT_ID}-${callRecord.callIdentifier}-${Date.now()}`
        }).promise();
        
        console.log('Started Step Function execution:', result.executionArn);
        
        // Mark call as processing
        await dynamodb.update({
            TableName: ACTIVE_CALLS_TABLE,
            Key: {
                tenantId: TENANT_ID,
                callIdentifier: callRecord.callIdentifier
            },
            UpdateExpression: 'SET #status = :status, stepFunctionArn = :arn',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': 'PROCESSING',
                ':arn': result.executionArn
            }
        }).promise();
        
        return result;
    } catch (error) {
        console.error('Error starting Step Function:', error);
        throw error;
    }
}
```

## Call Completion Step Function

### Step Function Definition
```json
{
  "Comment": "Process completed call and create fact_call record for tenant",
  "StartAt": "GetCallEvents",
  "States": {
    "GetCallEvents": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "${TenantId}-get-call-events",
        "Payload": {
          "tenantId.$": "$.tenantId",
          "callIdentifier.$": "$.callIdentifier"
        }
      },
      "ResultPath": "$.callEvents",
      "Next": "BuildCallFact",
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 2,
          "MaxAttempts": 3,
          "BackoffRate": 2.0
        }
      ]
    },
    "BuildCallFact": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "${TenantId}-call-fact-builder",
        "Payload": {
          "tenantId.$": "$.tenantId",
          "callIdentifier.$": "$.callIdentifier",
          "callEvents.$": "$.callEvents.Payload"
        }
      },
      "ResultPath": "$.factResult",
      "Next": "UpsertDimensions",
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 2,
          "MaxAttempts": 3,
          "BackoffRate": 2.0
        }
      ]
    },
    "UpsertDimensions": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "${TenantId}-dimension-manager",
        "Payload": {
          "tenantId.$": "$.tenantId",
          "callFact.$": "$.factResult.Payload.callFact",
          "agentInfo.$": "$.factResult.Payload.agentInfo",
          "queueInfo.$": "$.factResult.Payload.queueInfo"
        }
      },
      "ResultPath": "$.dimensionResult",
      "Next": "InsertCallFact",
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 2,
          "MaxAttempts": 3,
          "BackoffRate": 2.0
        }
      ]
    },
    "InsertCallFact": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "${TenantId}-redshift-insert",
        "Payload": {
          "tenantId.$": "$.tenantId",
          "tableName": "fact_call",
          "data.$": "$.factResult.Payload.callFact",
          "dimensionIds.$": "$.dimensionResult.Payload.dimensionIds"
        }
      },
      "ResultPath": "$.insertResult",
      "Next": "UpdateIntervals",
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 2,
          "MaxAttempts": 3,
          "BackoffRate": 2.0
        }
      ]
    },
    "UpdateIntervals": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "${TenantId}-interval-aggregator",
        "Payload": {
          "tenantId.$": "$.tenantId",
          "callFact.$": "$.factResult.Payload.callFact",
          "triggerType": "call_completed"
        }
      },
      "ResultPath": "$.aggregationResult",
      "Next": "CleanupCall",
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 2,
          "MaxAttempts": 3,
          "BackoffRate": 2.0
        }
      ]
    },
    "CleanupCall": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "${TenantId}-cleanup",
        "Payload": {
          "tenantId.$": "$.tenantId",
          "callIdentifier.$": "$.callIdentifier",
          "action": "mark_completed"
        }
      },
      "Next": "Success"
    },
    "Success": {
      "Type": "Succeed",
      "Result": {
        "status": "completed",
        "message": "Call fact processing completed successfully"
      }
    }
  }
}
```

## Call Fact Builder Lambda

### brandon911-call-fact-builder Lambda
```javascript
const AWS = require('aws-sdk');

const TENANT_ID = process.env.TENANT_ID;

exports.handler = async (event) => {
    const { tenantId, callIdentifier, callEvents } = event;
    
    console.log(`Building call fact for ${callIdentifier}, tenant: ${tenantId}`);
    
    try {
        const callFact = await buildCallFact(callEvents);
        const agentInfo = extractAgentInfo(callEvents);
        const queueInfo = extractQueueInfo(callEvents);
        
        return {
            statusCode: 200,
            body: {
                callFact: callFact,
                agentInfo: agentInfo,
                queueInfo: queueInfo
            }
        };
    } catch (error) {
        console.error('Error building call fact:', error);
        throw error;
    }
};

async function buildCallFact(callEvents) {
    // Sort events by timestamp
    const sortedEvents = callEvents.sort((a, b) => 
        new Date(a.timestamp) - new Date(b.timestamp)
    );
    
    const startEvent = sortedEvents.find(e => e.eventType === 'StartCall');
    const endEvent = sortedEvents.find(e => 
        e.eventType === 'EndCall' || e.eventType === 'EndMedia'
    );
    const answerEvent = sortedEvents.find(e => e.eventType === 'Answer');
    const cdrEvent = sortedEvents.find(e => e.eventType === 'CDRtype1');
    
    if (!startEvent) {
        throw new Error('No StartCall event found');
    }
    
    const callFact = {
        tenant_id: TENANT_ID,
        call_identifier: startEvent.data.callIdentifier,
        start_timestamp: startEvent.timestamp,
        end_timestamp: endEvent ? endEvent.timestamp : null,
        answer_timestamp: answerEvent ? answerEvent.timestamp : null,
        agent_name: startEvent.data.agent,
        queue_name: startEvent.data.queue || extractQueueFromAgency(startEvent.data.agencyOrElement),
        call_type: determineCallType(sortedEvents),
        talk_time_seconds: calculateTalkTime(answerEvent, endEvent),
        total_call_seconds: calculateTotalTime(startEvent, endEvent),
        abandoned_flag: !answerEvent,
        transferred_in_flag: hasTransferIn(sortedEvents),
        transferred_out_flag: hasTransferOut(sortedEvents),
        answered_le_10s_flag: calculateServiceLevel(startEvent, answerEvent, 10),
        answered_le_15s_flag: calculateServiceLevel(startEvent, answerEvent, 15),
        answered_le_20s_flag: calculateServiceLevel(startEvent, answerEvent, 20),
        answered_le_40s_flag: calculateServiceLevel(startEvent, answerEvent, 40),
        created_at: new Date().toISOString()
    };
    
    return callFact;
}

function determineCallType(events) {
    const hasOutbound = events.some(e => e.eventType === 'OutboundCall');
    const hasCDR = events.some(e => e.eventType === 'CDRtype1');
    
    if (hasOutbound) return 'Outbound';
    if (hasCDR) return 'Emergency';
    return 'Admin';
}

function calculateTalkTime(answerEvent, endEvent) {
    if (!answerEvent || !endEvent) return 0;
    
    const answerTime = new Date(answerEvent.timestamp);
    const endTime = new Date(endEvent.timestamp);
    
    return Math.max(0, Math.floor((endTime - answerTime) / 1000));
}

function calculateTotalTime(startEvent, endEvent) {
    if (!endEvent) return 0;
    
    const startTime = new Date(startEvent.timestamp);
    const endTime = new Date(endEvent.timestamp);
    
    return Math.max(0, Math.floor((endTime - startTime) / 1000));
}

function calculateServiceLevel(startEvent, answerEvent, thresholdSeconds) {
    if (!answerEvent) return false;
    
    const startTime = new Date(startEvent.timestamp);
    const answerTime = new Date(answerEvent.timestamp);
    const waitTime = Math.floor((answerTime - startTime) / 1000);
    
    return waitTime <= thresholdSeconds;
}

function hasTransferIn(events) {
    return events.some(e => 
        e.eventType === 'Transfer' && e.data.direction === 'in'
    );
}

function hasTransferOut(events) {
    return events.some(e => 
        e.eventType === 'Transfer' && e.data.direction === 'out'
    );
}

function extractQueueFromAgency(agencyOrElement) {
    // Extract queue name from agencyOrElement field
    // This depends on your specific naming convention
    return agencyOrElement || 'Unknown';
}

function extractAgentInfo(callEvents) {
    const startEvent = callEvents.find(e => e.eventType === 'StartCall');
    
    return {
        agent_name: startEvent?.data.agent,
        agent_uri: startEvent?.data.agentUri,
        workstation: startEvent?.data.workstation,
        operator_id: startEvent?.data.operatorId
    };
}

function extractQueueInfo(callEvents) {
    const startEvent = callEvents.find(e => e.eventType === 'StartCall');
    
    return {
        queue_name: startEvent?.data.queue || extractQueueFromAgency(startEvent?.data.agencyOrElement),
        ring_group_name: startEvent?.data.ringGroupName,
        ring_group_uri: startEvent?.data.ringGroupUri
    };
}
```

## Deployment Configuration (Per Tenant)

### CloudFormation Template
```yaml
AWSTemplateFormatVersion: '2010-09-09'
Description: 'Full AWS Native Event Processing for Tenant'

Parameters:
  TenantId:
    Type: String
    Default: brandon911
    Description: Tenant identifier
  
  RedshiftClusterEndpoint:
    Type: String
    Description: Redshift cluster endpoint

Resources:
  # SNS Topic
  TenantEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub "i3-events-${TenantId}"

  # DynamoDB Tables
  ActiveCallsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${TenantId}-active-calls"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: tenantId
          AttributeType: S
        - AttributeName: callIdentifier
          AttributeType: S
      KeySchema:
        - AttributeName: tenantId
          KeyType: HASH
        - AttributeName: callIdentifier
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true

  AgentSessionsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${TenantId}-agent-sessions"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: tenantId
          AttributeType: S
        - AttributeName: agentId
          AttributeType: S
      KeySchema:
        - AttributeName: tenantId
          KeyType: HASH
        - AttributeName: agentId
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true

  # Lambda Functions
  CallProcessorFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${TenantId}-call-processor"
      Runtime: nodejs18.x
      Handler: index.handler
      Timeout: 300
      Environment:
        Variables:
          TENANT_ID: !Ref TenantId
          ACTIVE_CALLS_TABLE: !Ref ActiveCallsTable
          CALL_COMPLETION_STATE_MACHINE_ARN: !Ref CallCompletionStateMachine

  CallFactBuilderFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${TenantId}-call-fact-builder"
      Runtime: nodejs18.x
      Handler: index.handler
      Environment:
        Variables:
          TENANT_ID: !Ref TenantId

  # Step Functions
  CallCompletionStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Sub "${TenantId}-call-completion"
      DefinitionString: !Sub |
        {
          "Comment": "Process completed call for ${TenantId}",
          "StartAt": "GetCallEvents",
          "States": {
            "GetCallEvents": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "${TenantId}-get-call-events",
                "Payload": {
                  "tenantId.$": "$.tenantId",
                  "callIdentifier.$": "$.callIdentifier"
                }
              },
              "End": true
            }
          }
        }

Outputs:
  TenantEventsTopic:
    Description: SNS Topic for tenant events
    Value: !Ref TenantEventsTopic
    Export:
      Name: !Sub "${TenantId}-EventsTopic"
```

## Benefits of Approach 2

1. **Full Serverless**: No EC2 infrastructure required
2. **Maximum Scalability**: Auto-scales to handle any event volume
3. **Real-time Processing**: Sub-second event processing
4. **Cost Efficiency**: Pay only for actual processing time
5. **Tenant Isolation**: Complete separation with dedicated resources
6. **Unified Architecture**: Single processing paradigm for all events
7. **Advanced Features**: Step Functions for complex workflows
8. **State Management**: DynamoDB for reliable state tracking

This full AWS native approach provides maximum scalability and real-time capabilities while maintaining complete tenant isolation.
