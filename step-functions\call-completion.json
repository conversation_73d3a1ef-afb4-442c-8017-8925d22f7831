{"Comment": "Process completed call and create fact_call record", "StartAt": "GetCallEvents", "States": {"GetCallEvents": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${GetCallEventsFunction}", "Payload": {"tenantId.$": "$.tenantId", "callIdentifier.$": "$.callIdentifier"}}, "ResultPath": "$.callEvents", "Next": "ProcessCallFact", "Retry": [{"ErrorEquals": ["States.TaskFailed"], "IntervalSeconds": 2, "MaxAttempts": 3, "BackoffRate": 2.0}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleError", "ResultPath": "$.error"}]}, "ProcessCallFact": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${CallFactBuilderFunction}", "Payload": {"tenantId.$": "$.tenantId", "callIdentifier.$": "$.callIdentifier", "callEvents.$": "$.callEvents.Payload"}}, "ResultPath": "$.factResult", "Next": "UpdateDimensions", "Retry": [{"ErrorEquals": ["States.TaskFailed"], "IntervalSeconds": 2, "MaxAttempts": 3, "BackoffRate": 2.0}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleError", "ResultPath": "$.error"}]}, "UpdateDimensions": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${DimensionManagerFunction}", "Payload": {"tenantId.$": "$.tenantId", "callFact.$": "$.factResult.Payload.callFact", "agentInfo.$": "$.factResult.Payload.agentInfo", "queueInfo.$": "$.factResult.Payload.queueInfo"}}, "ResultPath": "$.dimensionResult", "Next": "InsertCallFact", "Retry": [{"ErrorEquals": ["States.TaskFailed"], "IntervalSeconds": 2, "MaxAttempts": 3, "BackoffRate": 2.0}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleError", "ResultPath": "$.error"}]}, "InsertCallFact": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${RedshiftInsertFunction}", "Payload": {"tableName": "fact_call", "data.$": "$.factResult.Payload.callFact", "dimensionIds.$": "$.dimensionResult.Payload.dimensionIds"}}, "ResultPath": "$.insertResult", "Next": "TriggerIntervalAggregation", "Retry": [{"ErrorEquals": ["States.TaskFailed"], "IntervalSeconds": 2, "MaxAttempts": 3, "BackoffRate": 2.0}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleError", "ResultPath": "$.error"}]}, "TriggerIntervalAggregation": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${IntervalAggregatorFunction}", "Payload": {"tenantId.$": "$.tenantId", "callFact.$": "$.factResult.Payload.callFact", "triggerType": "call_completed"}}, "ResultPath": "$.aggregationResult", "Next": "CleanupCallRecord", "Retry": [{"ErrorEquals": ["States.TaskFailed"], "IntervalSeconds": 2, "MaxAttempts": 3, "BackoffRate": 2.0}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleError", "ResultPath": "$.error"}]}, "CleanupCallRecord": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${CleanupFunction}", "Payload": {"tenantId.$": "$.tenantId", "callIdentifier.$": "$.callIdentifier", "action": "mark_completed"}}, "ResultPath": "$.cleanupResult", "Next": "Success", "Retry": [{"ErrorEquals": ["States.TaskFailed"], "IntervalSeconds": 2, "MaxAttempts": 3, "BackoffRate": 2.0}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleError", "ResultPath": "$.error"}]}, "Success": {"Type": "Succeed", "Result": {"status": "completed", "message": "Call fact processing completed successfully"}}, "HandleError": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${ErrorHandlerFunction}", "Payload": {"tenantId.$": "$.tenantId", "callIdentifier.$": "$.callIdentifier", "error.$": "$.error", "context.$": "$"}}, "Next": "Fail"}, "Fail": {"Type": "Fail", "Cause": "Call fact processing failed"}}}