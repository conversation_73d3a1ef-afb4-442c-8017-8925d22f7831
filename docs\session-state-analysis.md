# Session State Analysis: DynamoDB vs Athena/Redshift

## Cost Analysis (Brandon911 Example)

### Assumptions
- **50 agents** working 24/7
- **Agent events**: Login/Logout (2/day), Available/Busy (60/day) = 62 events/agent/day
- **Total events**: 50 agents × 62 events = 3,100 events/day = 93,000/month
- **Session queries**: Each event needs to read current session = 93,000 reads/month

### Option 1: DynamoDB (Current)

#### Monthly Costs
```
Writes: 93,000 writes × $1.25/million = $0.12
Reads: 93,000 reads × $0.25/million = $0.02  
Storage: ~1GB × $0.25/GB = $0.25
Total: $0.39/month
```

#### Performance
- **Read Latency**: 1-3ms
- **Write Latency**: 1-3ms
- **Consistency**: Eventually consistent reads
- **Scalability**: Auto-scales to any load

#### Code Example
```javascript
// Current DynamoDB approach
async function getAgentSession(agentId) {
    const result = await dynamodb.get({
        TableName: 'brandon911-agent-sessions',
        Key: { tenantId: 'brandon911', agentId }
    }).promise();
    
    return result.Item; // 1-3ms response
}

async function updateAgentState(agentId, newState, timestamp) {
    await dynamodb.update({
        TableName: 'brandon911-agent-sessions',
        Key: { tenantId: 'brandon911', agentId },
        UpdateExpression: 'SET currentState = :state, currentStateStart = :start',
        ExpressionAttributeValues: {
            ':state': newState,
            ':start': timestamp
        }
    }).promise(); // 1-3ms response
}
```

### Option 2: Athena + S3

#### Monthly Costs
```
S3 Storage: 1GB × $0.023/GB = $0.02
Athena Queries: 93,000 queries × 1KB avg scan = 93MB
Query Cost: 0.000093TB × $5/TB = $0.0005
Total: $0.02/month (95% cheaper!)
```

#### Performance Issues
- **Read Latency**: 1-3 seconds (1000x slower!)
- **Write Latency**: Immediate to S3, but query lag
- **Consistency**: Eventually consistent (S3 propagation)
- **Cold Start**: First query can take 10+ seconds

#### Code Example
```javascript
// Athena approach - SLOW!
async function getAgentSession(agentId) {
    const query = `
        SELECT * FROM "brandon911_sessions"."agent_sessions"
        WHERE agent_id = '${agentId}' 
        AND session_end IS NULL
        ORDER BY session_start DESC
        LIMIT 1
    `;
    
    const result = await athena.startQueryExecution({
        QueryString: query,
        ResultConfiguration: {
            OutputLocation: 's3://brandon911-athena-results/'
        }
    }).promise();
    
    // Wait for query to complete (1-3 seconds!)
    await waitForQueryCompletion(result.QueryExecutionId);
    
    const data = await athena.getQueryResults({
        QueryExecutionId: result.QueryExecutionId
    }).promise();
    
    return parseAthenaResults(data); // 1-3 second response!
}
```

### Option 3: Redshift Direct

#### Monthly Costs
```
Redshift ra3.xlplus: $0.25/hour × 24 × 30 = $180/month
Queries: Included in cluster cost
Storage: Included up to 32TB
Total: $180/month (460x more expensive!)
```

#### Performance
- **Read Latency**: 50-200ms (good for analytics, slow for real-time)
- **Write Latency**: 50-200ms
- **Consistency**: Strong consistency
- **Concurrency**: Limited by cluster size

#### Code Example
```javascript
// Redshift approach - Medium performance, high cost
async function getAgentSession(agentId) {
    const sql = `
        SELECT * FROM brandon911_reporting.fact_agent_session
        WHERE agent_id = (
            SELECT agent_id FROM brandon911_reporting.dim_agent 
            WHERE agent_name = '${agentId}' AND is_current = true
        )
        AND logout_timestamp IS NULL
        ORDER BY login_timestamp DESC
        LIMIT 1
    `;
    
    const result = await redshift.executeStatement({
        ClusterIdentifier: 'brandon911-cluster',
        Database: 'reporting',
        Sql: sql
    }).promise();
    
    // Wait for query completion (50-200ms)
    const data = await redshift.getStatementResult({
        Id: result.Id
    }).promise();
    
    return parseRedshiftResults(data); // 50-200ms response
}
```

## Performance Comparison

| Metric | DynamoDB | Athena | Redshift |
|--------|----------|--------|----------|
| **Read Latency** | 1-3ms | 1-3 seconds | 50-200ms |
| **Write Latency** | 1-3ms | Immediate | 50-200ms |
| **Monthly Cost** | $0.39 | $0.02 | $180 |
| **Scalability** | Unlimited | Good | Limited |
| **Real-time Suitability** | ✅ Excellent | ❌ Poor | ⚠️ Marginal |

## Implications Analysis

### Using Athena for Session State

#### ❌ **Major Problems**
1. **Latency**: 1-3 second queries kill real-time processing
2. **Cold Starts**: First query can take 10+ seconds
3. **Concurrency**: Limited concurrent queries
4. **Complexity**: Need to manage S3 partitioning and file formats
5. **Eventual Consistency**: S3 propagation delays

#### ✅ **Only Benefit**
- **Cost**: 95% cheaper ($0.02 vs $0.39)

### Using Redshift for Session State

#### ❌ **Major Problems**
1. **Cost**: 460x more expensive ($180 vs $0.39)
2. **Latency**: 50-200ms too slow for real-time event processing
3. **Concurrency**: Limited by cluster capacity
4. **Overkill**: Using data warehouse for operational data

#### ✅ **Benefits**
- **Consistency**: Strong consistency
- **Integration**: Already using Redshift for reporting

## Robustness Analysis: Collector API → Redshift ETL

### Current ETL Flow
```
Collector API → MariaDB → ETL Lambda → Redshift
```

### Robustness Assessment

#### ✅ **Strengths**
1. **Proven Path**: Collector API is battle-tested
2. **Data Quality**: ETL can apply business rules and validation
3. **Error Handling**: Can retry failed transformations
4. **Flexibility**: Can handle schema changes and data corrections

#### ⚠️ **Potential Issues**
1. **Latency**: 5-minute delay for ETL processing
2. **Complexity**: Multiple hops increase failure points
3. **Consistency**: Temporary inconsistency between MariaDB and Redshift
4. **Scaling**: ETL Lambda needs to handle increasing data volume

### ETL Robustness Example

```javascript
// brandon911-etl-transformer - Robust implementation
exports.handler = async (event) => {
    const batchSize = 1000;
    let processedCount = 0;
    let errorCount = 0;
    
    try {
        // Get unprocessed callsummary records
        const unprocessedRecords = await getUnprocessedCallsummary(batchSize);
        
        for (const batch of chunk(unprocessedRecords, 100)) {
            try {
                await processBatch(batch);
                processedCount += batch.length;
                
                // Mark as processed
                await markAsProcessed(batch.map(r => r.id));
                
            } catch (batchError) {
                console.error('Batch processing error:', batchError);
                errorCount += batch.length;
                
                // Mark as failed for retry
                await markAsFailed(batch.map(r => r.id), batchError.message);
            }
        }
        
        // Send metrics to CloudWatch
        await cloudwatch.putMetricData({
            Namespace: 'Brandon911/ETL',
            MetricData: [
                {
                    MetricName: 'RecordsProcessed',
                    Value: processedCount,
                    Unit: 'Count'
                },
                {
                    MetricName: 'RecordsFailed', 
                    Value: errorCount,
                    Unit: 'Count'
                }
            ]
        }).promise();
        
        return {
            statusCode: 200,
            body: {
                processed: processedCount,
                failed: errorCount,
                success: errorCount === 0
            }
        };
        
    } catch (error) {
        console.error('ETL processing failed:', error);
        
        // Send alert to SNS
        await sns.publish({
            TopicArn: 'arn:aws:sns:region:account:brandon911-etl-alerts',
            Subject: 'ETL Processing Failed',
            Message: `ETL failed for brandon911: ${error.message}`
        }).promise();
        
        throw error;
    }
};

async function processBatch(records) {
    // Transform callsummary to fact_call format
    const factRecords = records.map(transformToFactCall);
    
    // Validate data quality
    const validRecords = factRecords.filter(validateFactCall);
    
    if (validRecords.length !== factRecords.length) {
        console.warn(`Data quality issues: ${factRecords.length - validRecords.length} invalid records`);
    }
    
    // Bulk insert to Redshift
    await bulkInsertToRedshift(validRecords);
}

function transformToFactCall(callsummary) {
    return {
        tenant_id: 'brandon911',
        call_identifier: callsummary.call_identifier,
        agent_id: lookupAgentId(callsummary.agent),
        queue_id: lookupQueueId(callsummary.queue),
        start_timestamp: callsummary.start_time,
        end_timestamp: callsummary.end_time,
        talk_time_sec: callsummary.talk_duration,
        total_call_sec: callsummary.total_duration,
        answered_le_10s_flag: callsummary.ring_time <= 10,
        answered_le_15s_flag: callsummary.ring_time <= 15,
        answered_le_20s_flag: callsummary.ring_time <= 20,
        answered_le_40s_flag: callsummary.ring_time <= 40,
        source_system: 'collector_api',
        created_at: new Date().toISOString()
    };
}

function validateFactCall(record) {
    return record.call_identifier && 
           record.start_timestamp && 
           record.talk_time_sec >= 0 &&
           record.total_call_sec >= record.talk_time_sec;
}
```

## Recommendation

### ✅ **Keep DynamoDB for Session State**

**Reasons:**
1. **Cost is Negligible**: $0.39/month vs $0.02/month - the $0.37 difference is insignificant
2. **Performance is Critical**: 1-3ms vs 1-3 seconds is a 1000x difference
3. **Real-time Requirements**: Agent state changes need immediate processing
4. **Simplicity**: DynamoDB is purpose-built for this use case

### ✅ **ETL Approach is Robust**

**Reasons:**
1. **Proven Pattern**: ETL is industry standard for data warehousing
2. **Error Handling**: Can implement retry logic and data quality checks
3. **Flexibility**: Can handle business rule changes without touching Collector API
4. **Monitoring**: Easy to add CloudWatch metrics and alerts

### 🎯 **Optimized Architecture for Brandon911**

```
i3 Events → SNS brandon911 → Agent Processor → DynamoDB (session state)
                          → Agent Processor → Redshift (fact tables)
                          → Call Processor → Collector API → MariaDB
                          
MariaDB → ETL Lambda (5min) → Redshift fact_call

Redshift → Power BI (hourly refresh)
```

**Total Monthly Cost**: ~$200 (mostly Redshift cluster)
**Performance**: Sub-second agent processing, 5-minute call data availability
**Robustness**: Proven components with comprehensive error handling
