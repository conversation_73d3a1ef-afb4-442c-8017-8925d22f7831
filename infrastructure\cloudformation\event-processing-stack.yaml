AWSTemplateFormatVersion: '2010-09-09'
Description: 'Real-time i3 Event Processing Infrastructure for ACD/Agent Reporting'

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name
  
  RedshiftClusterEndpoint:
    Type: String
    Description: Redshift cluster endpoint
  
  RedshiftDatabase:
    Type: String
    Default: reporting
    Description: Redshift database name
  
  RedshiftUsername:
    Type: String
    Description: Redshift username
  
  RedshiftPassword:
    Type: String
    NoEcho: true
    Description: Redshift password

Resources:
  # =====================================================
  # SQS QUEUES
  # =====================================================
  
  CallEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "${Environment}-call-events"
      VisibilityTimeoutSeconds: 300
      MessageRetentionPeriod: 1209600  # 14 days
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt CallEventsDeadLetterQueue.Arn
        maxReceiveCount: 3
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Purpose
          Value: CallEventProcessing

  CallEventsDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "${Environment}-call-events-dlq"
      MessageRetentionPeriod: 1209600  # 14 days

  AgentEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "${Environment}-agent-events"
      VisibilityTimeoutSeconds: 300
      MessageRetentionPeriod: 1209600  # 14 days
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt AgentEventsDeadLetterQueue.Arn
        maxReceiveCount: 3

  AgentEventsDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "${Environment}-agent-events-dlq"
      MessageRetentionPeriod: 1209600  # 14 days

  QueueEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "${Environment}-queue-events"
      VisibilityTimeoutSeconds: 300
      MessageRetentionPeriod: 1209600  # 14 days
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt QueueEventsDeadLetterQueue.Arn
        maxReceiveCount: 3

  QueueEventsDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "${Environment}-queue-events-dlq"
      MessageRetentionPeriod: 1209600  # 14 days

  # =====================================================
  # DYNAMODB TABLES
  # =====================================================

  ActiveCallsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${Environment}-active-calls"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: tenantId
          AttributeType: S
        - AttributeName: callIdentifier
          AttributeType: S
      KeySchema:
        - AttributeName: tenantId
          KeyType: HASH
        - AttributeName: callIdentifier
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      Tags:
        - Key: Environment
          Value: !Ref Environment

  AgentSessionsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${Environment}-agent-sessions"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: tenantId
          AttributeType: S
        - AttributeName: agentId
          AttributeType: S
      KeySchema:
        - AttributeName: tenantId
          KeyType: HASH
        - AttributeName: agentId
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      Tags:
        - Key: Environment
          Value: !Ref Environment

  QueueStatesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${Environment}-queue-states"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: tenantId
          AttributeType: S
        - AttributeName: queueId
          AttributeType: S
      KeySchema:
        - AttributeName: tenantId
          KeyType: HASH
        - AttributeName: queueId
          KeyType: RANGE
      Tags:
        - Key: Environment
          Value: !Ref Environment

  # =====================================================
  # IAM ROLES
  # =====================================================

  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${Environment}-event-processing-lambda-role"
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: EventProcessingPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - sqs:ReceiveMessage
                  - sqs:DeleteMessage
                  - sqs:GetQueueAttributes
                  - sqs:SendMessage
                Resource:
                  - !GetAtt CallEventsQueue.Arn
                  - !GetAtt AgentEventsQueue.Arn
                  - !GetAtt QueueEventsQueue.Arn
                  - !GetAtt CallEventsDeadLetterQueue.Arn
                  - !GetAtt AgentEventsDeadLetterQueue.Arn
                  - !GetAtt QueueEventsDeadLetterQueue.Arn
              - Effect: Allow
                Action:
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                  - dynamodb:Query
                  - dynamodb:Scan
                Resource:
                  - !GetAtt ActiveCallsTable.Arn
                  - !GetAtt AgentSessionsTable.Arn
                  - !GetAtt QueueStatesTable.Arn
              - Effect: Allow
                Action:
                  - states:StartExecution
                Resource: !Ref CallCompletionStateMachine
              - Effect: Allow
                Action:
                  - s3:GetObject
                Resource: "*"
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                  - ssm:GetParameters
                Resource: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${Environment}/event-processing/*"

  StepFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${Environment}-step-function-role"
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: states.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: StepFunctionPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource: !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${Environment}-*"

  # =====================================================
  # LAMBDA FUNCTIONS
  # =====================================================

  EventRouterFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${Environment}-event-router"
      Runtime: nodejs18.x
      Handler: index.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 300
      MemorySize: 512
      Environment:
        Variables:
          CALL_EVENTS_QUEUE_URL: !Ref CallEventsQueue
          AGENT_EVENTS_QUEUE_URL: !Ref AgentEventsQueue
          QUEUE_EVENTS_QUEUE_URL: !Ref QueueEventsQueue
          DLQ_URL: !GetAtt CallEventsDeadLetterQueue.Arn
      Code:
        ZipFile: |
          exports.handler = async (event) => {
            console.log('Event Router placeholder');
            return { statusCode: 200 };
          };

  CallEventProcessorFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${Environment}-call-event-processor"
      Runtime: nodejs18.x
      Handler: index.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 300
      MemorySize: 512
      Environment:
        Variables:
          ACTIVE_CALLS_TABLE: !Ref ActiveCallsTable
          CALL_COMPLETION_STATE_MACHINE_ARN: !Ref CallCompletionStateMachine
      Code:
        ZipFile: |
          exports.handler = async (event) => {
            console.log('Call Event Processor placeholder');
            return { statusCode: 200 };
          };

  # =====================================================
  # STEP FUNCTIONS
  # =====================================================

  CallCompletionStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Sub "${Environment}-call-completion"
      RoleArn: !GetAtt StepFunctionRole.Arn
      DefinitionString: !Sub |
        {
          "Comment": "Process completed call and create fact_call record",
          "StartAt": "Success",
          "States": {
            "Success": {
              "Type": "Succeed"
            }
          }
        }

Outputs:
  CallEventsQueueUrl:
    Description: URL of the Call Events SQS Queue
    Value: !Ref CallEventsQueue
    Export:
      Name: !Sub "${Environment}-CallEventsQueueUrl"

  AgentEventsQueueUrl:
    Description: URL of the Agent Events SQS Queue
    Value: !Ref AgentEventsQueue
    Export:
      Name: !Sub "${Environment}-AgentEventsQueueUrl"

  ActiveCallsTableName:
    Description: Name of the Active Calls DynamoDB Table
    Value: !Ref ActiveCallsTable
    Export:
      Name: !Sub "${Environment}-ActiveCallsTableName"
