# Approach 1: Hybrid Implementation (Collector API + AWS Native)

## Architecture Overview

```mermaid
graph TB
    subgraph "Event Sources"
        S3[S3 i3 XML Logs<br/>brandon911-bucket]
        SNS[SNS Topic<br/>i3-events-brandon911]
    end
    
    subgraph "SNS Filtering"
        CF[Call Filter<br/>event_type: StartCall,EndCall]
        AF[Agent Filter<br/>event_type: Login,Logout,Available]
    end
    
    subgraph "Call Processing (Existing)"
        CL[brandon911-call-processor<br/>Lambda]
        CA[Collector API<br/>EC2 Instance]
        MDB[(MariaDB<br/>callsummary)]
        ETL[ETL Job<br/>callsummary → Redshift]
    end
    
    subgraph "Agent Processing (New AWS Native)"
        AL[brandon911-agent-processor<br/>Lambda]
        DDB[(DynamoDB<br/>brandon911-agent-sessions)]
        SF[Step Functions<br/>Agent Session Workflow]
    end
    
    subgraph "Data Warehouse"
        RS[(Redshift<br/>brandon911_reporting)]
        PBI[Power BI<br/>Hourly Refresh]
    end
    
    S3 --> SNS
    SNS --> CF
    SNS --> AF
    
    CF --> CL
    CL --> CA
    CA --> MDB
    MDB --> ETL
    ETL --> RS
    
    AF --> AL
    AL --> DDB
    AL --> SF
    SF --> RS
    
    RS --> PBI
```

## SNS Topic Configuration

### Topic Setup per Tenant
```bash
# Create tenant-specific SNS topic
aws sns create-topic --name "i3-events-brandon911"

# Create subscription with message filtering
aws sns subscribe \
  --topic-arn "arn:aws:sns:region:account:i3-events-brandon911" \
  --protocol lambda \
  --notification-endpoint "arn:aws:lambda:region:account:function:brandon911-call-processor" \
  --attributes '{
    "FilterPolicy": "{\"tenant_id\":[\"brandon911\"],\"event_type\":[\"StartCall\",\"EndCall\",\"Answer\",\"CDRType1\"]}"
  }'

aws sns subscribe \
  --topic-arn "arn:aws:sns:region:account:i3-events-brandon911" \
  --protocol lambda \
  --notification-endpoint "arn:aws:lambda:region:account:function:brandon911-agent-processor" \
  --attributes '{
    "FilterPolicy": "{\"tenant_id\":[\"brandon911\"],\"event_type\":[\"Login\",\"Logout\",\"Available\",\"BusiedOut\",\"ACDLogin\",\"ACDLogout\"]}"
  }'
```

## Call Processing Lambda (Minimal Changes)

### brandon911-call-processor Lambda
```javascript
const AWS = require('aws-sdk');
const axios = require('axios');

const COLLECTOR_API_ENDPOINT = process.env.COLLECTOR_API_ENDPOINT;
const TENANT_ID = process.env.TENANT_ID;

exports.handler = async (event) => {
    console.log(`Processing call events for tenant: ${TENANT_ID}`);
    
    const results = [];
    
    for (const record of event.Records) {
        try {
            const snsMessage = JSON.parse(record.Sns.Message);
            const result = await processCallEvent(snsMessage);
            results.push(result);
        } catch (error) {
            console.error('Error processing call event:', error);
            results.push({ success: false, error: error.message });
        }
    }
    
    return {
        statusCode: 200,
        body: JSON.stringify({
            tenant: TENANT_ID,
            processed: results.length,
            results: results
        })
    };
};

async function processCallEvent(snsMessage) {
    const { eventType, parsedEvent, timestamp, s3Key } = snsMessage;
    
    console.log(`Processing ${eventType} for tenant ${TENANT_ID}`);
    
    // Transform to existing Collector API format
    const collectorPayload = {
        tenant_id: TENANT_ID,
        event_type: eventType,
        timestamp: timestamp,
        s3_key: s3Key,
        event_data: parsedEvent,
        // Map to existing collector API fields
        agency_or_element: parsedEvent.agencyOrElement,
        call_identifier: parsedEvent.callIdentifier,
        agent: parsedEvent.agent,
        queue: parsedEvent.queue
    };
    
    // Send to existing Collector API
    try {
        const response = await axios.post(
            `${COLLECTOR_API_ENDPOINT}/api/events/call`,
            collectorPayload,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Tenant-ID': TENANT_ID
                },
                timeout: 30000
            }
        );
        
        return {
            success: true,
            eventType: eventType,
            collectorResponse: response.status,
            callIdentifier: parsedEvent.callIdentifier
        };
    } catch (error) {
        console.error('Collector API error:', error);
        throw error;
    }
}
```

## Agent Processing Lambda (New AWS Native)

### brandon911-agent-processor Lambda
```javascript
const AWS = require('aws-sdk');

const dynamodb = new AWS.DynamoDB.DocumentClient();
const stepfunctions = new AWS.StepFunctions();
const redshift = new AWS.RedshiftData();

const TENANT_ID = process.env.TENANT_ID;
const AGENT_SESSIONS_TABLE = `${TENANT_ID}-agent-sessions`;
const AGENT_WORKFLOW_ARN = process.env.AGENT_WORKFLOW_ARN;
const REDSHIFT_CLUSTER = process.env.REDSHIFT_CLUSTER;
const REDSHIFT_DATABASE = process.env.REDSHIFT_DATABASE;
const REDSHIFT_SCHEMA = `${TENANT_ID}_reporting`;

exports.handler = async (event) => {
    console.log(`Processing agent events for tenant: ${TENANT_ID}`);
    
    const results = [];
    
    for (const record of event.Records) {
        try {
            const snsMessage = JSON.parse(record.Sns.Message);
            const result = await processAgentEvent(snsMessage);
            results.push(result);
        } catch (error) {
            console.error('Error processing agent event:', error);
            results.push({ success: false, error: error.message });
        }
    }
    
    return {
        statusCode: 200,
        body: JSON.stringify({
            tenant: TENANT_ID,
            processed: results.length,
            results: results
        })
    };
};

async function processAgentEvent(snsMessage) {
    const { eventType, parsedEvent, timestamp } = snsMessage;
    const agentId = parsedEvent.agent || parsedEvent.agentName;
    
    console.log(`Processing ${eventType} for agent ${agentId}, tenant ${TENANT_ID}`);
    
    switch (eventType) {
        case 'Login':
            return await processLogin(agentId, parsedEvent, timestamp);
        case 'Logout':
            return await processLogout(agentId, parsedEvent, timestamp);
        case 'Available':
            return await processAvailable(agentId, parsedEvent, timestamp);
        case 'BusiedOut':
            return await processBusiedOut(agentId, parsedEvent, timestamp);
        case 'ACDLogin':
            return await processACDLogin(agentId, parsedEvent, timestamp);
        case 'ACDLogout':
            return await processACDLogout(agentId, parsedEvent, timestamp);
        default:
            console.log(`Unknown agent event type: ${eventType}`);
            return { success: true, eventType, action: 'ignored' };
    }
}

async function processLogin(agentId, parsedEvent, timestamp) {
    // End any existing session
    await endExistingSession(agentId, timestamp);
    
    // Create new session
    const sessionData = {
        tenantId: TENANT_ID,
        agentId: agentId,
        sessionStart: timestamp,
        currentState: 'LOGGED_IN',
        currentStateStart: timestamp,
        agentRole: parsedEvent.agentRole,
        tenantGroup: parsedEvent.tenantGroup,
        operatorId: parsedEvent.operatorId,
        workstation: parsedEvent.workstation,
        deviceName: parsedEvent.deviceName,
        agentUri: parsedEvent.agentUri,
        queueAssignments: [],
        stateHistory: [{
            state: 'LOGGED_IN',
            startTime: timestamp,
            duration: null
        }],
        ttl: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days
        createdAt: new Date().toISOString()
    };
    
    await dynamodb.put({
        TableName: AGENT_SESSIONS_TABLE,
        Item: sessionData
    }).promise();
    
    // Upsert agent dimension in Redshift
    await upsertAgentDimension(agentId, parsedEvent);
    
    return { 
        success: true, 
        eventType: 'Login', 
        agentId, 
        action: 'session_started' 
    };
}

async function processLogout(agentId, parsedEvent, timestamp) {
    const session = await getAgentSession(agentId);
    
    if (!session) {
        console.log(`No active session found for agent ${agentId}`);
        return { success: true, eventType: 'Logout', agentId, action: 'no_session' };
    }
    
    // Calculate session duration
    const sessionDuration = Math.floor(
        (new Date(timestamp) - new Date(session.sessionStart)) / 1000
    );
    
    // Update session with logout
    await dynamodb.update({
        TableName: AGENT_SESSIONS_TABLE,
        Key: { tenantId: TENANT_ID, agentId },
        UpdateExpression: `SET 
            sessionEnd = :sessionEnd,
            currentState = :state,
            sessionDuration = :duration,
            updatedAt = :updatedAt`,
        ExpressionAttributeValues: {
            ':sessionEnd': timestamp,
            ':state': 'LOGGED_OUT',
            ':duration': sessionDuration,
            ':updatedAt': new Date().toISOString()
        }
    }).promise();
    
    // Trigger Step Function for session completion
    await stepfunctions.startExecution({
        stateMachineArn: AGENT_WORKFLOW_ARN,
        input: JSON.stringify({
            tenantId: TENANT_ID,
            agentId: agentId,
            sessionEnd: timestamp,
            action: 'session_completed'
        }),
        name: `agent-session-${TENANT_ID}-${agentId}-${Date.now()}`
    }).promise();
    
    return { 
        success: true, 
        eventType: 'Logout', 
        agentId, 
        sessionDuration,
        action: 'session_ended' 
    };
}

async function getAgentSession(agentId) {
    try {
        const result = await dynamodb.get({
            TableName: AGENT_SESSIONS_TABLE,
            Key: { tenantId: TENANT_ID, agentId }
        }).promise();
        
        return result.Item;
    } catch (error) {
        console.error('Error getting agent session:', error);
        return null;
    }
}

async function upsertAgentDimension(agentId, parsedEvent) {
    const sql = `
        CALL ${REDSHIFT_SCHEMA}.upsert_agent(
            '${TENANT_ID}',
            '${agentId}',
            '${parsedEvent.agentRole || ''}',
            '${parsedEvent.agentUri || ''}',
            '${parsedEvent.operatorId || ''}',
            '${parsedEvent.workstation || ''}'
        );
    `;
    
    try {
        await redshift.executeStatement({
            ClusterIdentifier: REDSHIFT_CLUSTER,
            Database: REDSHIFT_DATABASE,
            Sql: sql
        }).promise();
    } catch (error) {
        console.error('Error upserting agent dimension:', error);
    }
}

async function endExistingSession(agentId, timestamp) {
    const existingSession = await getAgentSession(agentId);
    
    if (existingSession && !existingSession.sessionEnd) {
        await processLogout(agentId, {}, timestamp);
    }
}
```

## Redshift ETL for Call Data

### Scheduled ETL Job (CloudWatch Events)
```javascript
const AWS = require('aws-sdk');

const redshift = new AWS.RedshiftData();
const TENANT_ID = process.env.TENANT_ID;
const REDSHIFT_SCHEMA = `${TENANT_ID}_reporting`;

exports.handler = async (event) => {
    console.log(`Running ETL for tenant: ${TENANT_ID}`);
    
    try {
        // Transform callsummary to fact_call
        await transformCallsummaryToFacts();
        
        // Update dimensions from call data
        await updateDimensionsFromCalls();
        
        return {
            statusCode: 200,
            body: JSON.stringify({
                tenant: TENANT_ID,
                message: 'ETL completed successfully'
            })
        };
    } catch (error) {
        console.error('ETL error:', error);
        throw error;
    }
};

async function transformCallsummaryToFacts() {
    const sql = `
        CALL ${REDSHIFT_SCHEMA}.sp_transform_callsummary_to_facts('${TENANT_ID}');
    `;
    
    await redshift.executeStatement({
        ClusterIdentifier: process.env.REDSHIFT_CLUSTER,
        Database: process.env.REDSHIFT_DATABASE,
        Sql: sql
    }).promise();
}
```

## Deployment Configuration

### CloudFormation Template (Per Tenant)
```yaml
Parameters:
  TenantId:
    Type: String
    Default: brandon911
    Description: Tenant identifier
  
  CollectorApiEndpoint:
    Type: String
    Description: Collector API endpoint for this tenant

Resources:
  # SNS Topic for tenant
  TenantEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub "i3-events-${TenantId}"
      
  # DynamoDB table for agent sessions
  AgentSessionsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${TenantId}-agent-sessions"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: tenantId
          AttributeType: S
        - AttributeName: agentId
          AttributeType: S
      KeySchema:
        - AttributeName: tenantId
          KeyType: HASH
        - AttributeName: agentId
          KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true

  # Call processor Lambda
  CallProcessorFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${TenantId}-call-processor"
      Runtime: nodejs18.x
      Handler: index.handler
      Environment:
        Variables:
          TENANT_ID: !Ref TenantId
          COLLECTOR_API_ENDPOINT: !Ref CollectorApiEndpoint
      Code:
        ZipFile: |
          // Call processor code here

  # Agent processor Lambda  
  AgentProcessorFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${TenantId}-agent-processor"
      Runtime: nodejs18.x
      Handler: index.handler
      Environment:
        Variables:
          TENANT_ID: !Ref TenantId
          REDSHIFT_SCHEMA: !Sub "${TenantId}_reporting"
      Code:
        ZipFile: |
          // Agent processor code here
```

## Benefits of Approach 1

1. **Low Risk**: Existing call processing infrastructure unchanged
2. **Incremental**: Can implement tenant by tenant
3. **Proven**: Collector API is battle-tested for call processing
4. **Fast Implementation**: Only agent processing needs development
5. **Tenant Isolation**: Complete separation via SNS filtering and dedicated Lambdas
6. **Cost Effective**: Shared EC2 for call processing, serverless for agent processing

This hybrid approach provides the best of both worlds: proven call processing with modern serverless agent processing.
