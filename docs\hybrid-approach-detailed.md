# Hybrid Approach: Detailed End-to-End Implementation

## Architecture Overview

The hybrid approach combines existing Collector API infrastructure for call processing with new AWS native processing for agent/queue events, providing complete tenant isolation while minimizing risk.

### Key Principles
- **Full Tenant Isolation**: Each tenant has dedicated AWS resources (except shared Collector API and Redshift)
- **SNS Filtering**: Direct event routing without SQS queues
- **Dual Processing**: Collector API for calls, AWS native for agents/queues
- **Real-time Intervals**: 5-minute aggregations for near real-time dashboards

## Detailed End-to-End Flow

### 1. Event Ingestion & Parsing

#### XML Event Processing
```javascript
// Event Router Lambda (Shared)
exports.handler = async (event) => {
    for (const record of event.Records) {
        const s3Event = record.s3;
        const bucket = s3Event.bucket.name;
        const key = s3Event.object.key;
        
        // Extract tenant from bucket name: brandon911-i3logs
        const tenantId = bucket.split('-')[0];
        
        // Download and parse XML
        const xmlContent = await s3.getObject({ Bucket: bucket, Key: key }).promise();
        const events = await parseI3XML(xmlContent.Body);
        
        // Route each event to tenant-specific SNS topic
        for (const event of events) {
            await routeEvent(tenantId, event);
        }
    }
};

async function parseI3XML(xmlContent) {
    const parser = new xml2js.Parser();
    const result = await parser.parseStringPromise(xmlContent);
    
    const events = [];
    
    // Extract different event types
    if (result.i3log.StartCall) {
        events.push(...result.i3log.StartCall.map(e => ({
            eventType: 'StartCall',
            callIdentifier: e.callIdentifier[0],
            agent: e.agent[0],
            queue: e.queue?.[0],
            agencyOrElement: e.agencyOrElement[0],
            timestamp: e.timestamp[0]
        })));
    }
    
    if (result.i3log.Login) {
        events.push(...result.i3log.Login.map(e => ({
            eventType: 'Login',
            agent: e.agent[0],
            agentRole: e.agentRole[0],
            tenantGroup: e.tenantGroup[0],
            operatorId: e.operatorId[0],
            workstation: e.workstation[0],
            deviceName: e.deviceName[0],
            agentUri: e.agentUri[0],
            timestamp: e.timestamp[0]
        })));
    }
    
    // ... parse other event types
    
    return events;
}

async function routeEvent(tenantId, event) {
    const eventCategory = getEventCategory(event.eventType);
    
    const snsParams = {
        TopicArn: `arn:aws:sns:${region}:${account}:i3-events-${tenantId}`,
        Message: JSON.stringify({
            tenantId: tenantId,
            eventType: event.eventType,
            parsedEvent: event,
            timestamp: event.timestamp,
            receivedAt: new Date().toISOString()
        }),
        MessageAttributes: {
            tenant_id: { DataType: 'String', StringValue: tenantId },
            event_type: { DataType: 'String', StringValue: event.eventType },
            event_category: { DataType: 'String', StringValue: eventCategory }
        }
    };
    
    await sns.publish(snsParams).promise();
}

function getEventCategory(eventType) {
    const callEvents = ['StartCall', 'EndCall', 'Answer', 'CDRtype1', 'OutboundCall'];
    const agentEvents = ['Login', 'Logout', 'Available', 'BusiedOut', 'ACDLogin', 'ACDLogout'];
    const queueEvents = ['QueueStateChange'];
    
    if (callEvents.includes(eventType)) return 'call';
    if (agentEvents.includes(eventType)) return 'agent';
    if (queueEvents.includes(eventType)) return 'queue';
    return 'unknown';
}
```

### 2. SNS Topic Filtering & Routing

#### Per-Tenant SNS Configuration
```bash
# Create tenant-specific SNS topics
aws sns create-topic --name "i3-events-brandon911"
aws sns create-topic --name "i3-events-client2"

# Subscribe call processor with filter
aws sns subscribe \
  --topic-arn "arn:aws:sns:region:account:i3-events-brandon911" \
  --protocol lambda \
  --notification-endpoint "arn:aws:lambda:region:account:function:brandon911-call-processor" \
  --attributes '{
    "FilterPolicy": "{\"tenant_id\":[\"brandon911\"],\"event_category\":[\"call\"]}"
  }'

# Subscribe agent processor with filter
aws sns subscribe \
  --topic-arn "arn:aws:sns:region:account:i3-events-brandon911" \
  --protocol lambda \
  --notification-endpoint "arn:aws:lambda:region:account:function:brandon911-agent-processor" \
  --attributes '{
    "FilterPolicy": "{\"tenant_id\":[\"brandon911\"],\"event_category\":[\"agent\"]}"
  }'
```

### 3. Call Processing Pipeline (Existing + Minimal Changes)

#### Call Processor Lambda
```javascript
// brandon911-call-processor Lambda
const COLLECTOR_API_ENDPOINT = process.env.COLLECTOR_API_ENDPOINT;
const TENANT_ID = process.env.TENANT_ID;

exports.handler = async (event) => {
    for (const record of event.Records) {
        const snsMessage = JSON.parse(record.Sns.Message);
        await forwardToCollectorAPI(snsMessage);
    }
};

async function forwardToCollectorAPI(snsMessage) {
    const { eventType, parsedEvent, timestamp } = snsMessage;
    
    // Transform to existing Collector API format
    const payload = {
        tenant_id: TENANT_ID,
        event_type: eventType,
        timestamp: timestamp,
        // Map to existing collector fields
        call_identifier: parsedEvent.callIdentifier,
        agent: parsedEvent.agent,
        queue: parsedEvent.queue,
        agency_or_element: parsedEvent.agencyOrElement,
        raw_event_data: parsedEvent
    };
    
    // Send to existing Collector API
    await axios.post(`${COLLECTOR_API_ENDPOINT}/api/events/call`, payload, {
        headers: { 'X-Tenant-ID': TENANT_ID }
    });
}
```

#### Collector API Changes (Minimal)
```javascript
// Existing Collector API - Add Redshift writing
app.post('/api/events/call', async (req, res) => {
    const tenantId = req.headers['x-tenant-id'];
    
    // Existing logic - write to MariaDB
    await writeToMariaDB(req.body);
    
    // NEW: Also write to Redshift for immediate availability
    await writeToRedshift(tenantId, req.body);
    
    res.json({ success: true });
});

async function writeToRedshift(tenantId, eventData) {
    // Write to tenant-specific schema
    const schema = `${tenantId}_reporting`;
    
    // Insert into raw_callsummary table for ETL processing
    const sql = `
        INSERT INTO ${schema}.raw_callsummary (
            tenant_id, call_identifier, event_type, 
            agent, queue, timestamp, raw_data
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    await redshift.query(sql, [
        tenantId, eventData.call_identifier, eventData.event_type,
        eventData.agent, eventData.queue, eventData.timestamp,
        JSON.stringify(eventData.raw_event_data)
    ]);
}
```

### 4. Agent Processing Pipeline (New AWS Native)

#### Agent Processor Lambda
```javascript
// brandon911-agent-processor Lambda
const TENANT_ID = process.env.TENANT_ID;
const AGENT_SESSIONS_TABLE = `${TENANT_ID}-agent-sessions`;
const REDSHIFT_SCHEMA = `${TENANT_ID}_reporting`;

exports.handler = async (event) => {
    for (const record of event.Records) {
        const snsMessage = JSON.parse(record.Sns.Message);
        await processAgentEvent(snsMessage);
    }
};

async function processAgentEvent(snsMessage) {
    const { eventType, parsedEvent, timestamp } = snsMessage;
    const agentId = parsedEvent.agent;
    
    switch (eventType) {
        case 'Login':
            await processLogin(agentId, parsedEvent, timestamp);
            break;
        case 'Logout':
            await processLogout(agentId, parsedEvent, timestamp);
            break;
        case 'Available':
            await processStateChange(agentId, 'AVAILABLE', parsedEvent, timestamp);
            break;
        case 'BusiedOut':
            await processStateChange(agentId, 'BUSY_OTHER', parsedEvent, timestamp);
            break;
        case 'ACDLogin':
            await processACDLogin(agentId, parsedEvent, timestamp);
            break;
        case 'ACDLogout':
            await processACDLogout(agentId, parsedEvent, timestamp);
            break;
    }
}

async function processLogin(agentId, parsedEvent, timestamp) {
    // End any existing session
    await endExistingSession(agentId, timestamp);
    
    // Create new session in DynamoDB
    const sessionData = {
        tenantId: TENANT_ID,
        agentId: agentId,
        sessionStart: timestamp,
        currentState: 'LOGGED_IN',
        currentStateStart: timestamp,
        agentRole: parsedEvent.agentRole,
        tenantGroup: parsedEvent.tenantGroup,
        operatorId: parsedEvent.operatorId,
        workstation: parsedEvent.workstation,
        deviceName: parsedEvent.deviceName,
        agentUri: parsedEvent.agentUri,
        queueAssignments: [],
        ttl: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
    };
    
    await dynamodb.put({
        TableName: AGENT_SESSIONS_TABLE,
        Item: sessionData
    }).promise();
    
    // Upsert agent dimension in Redshift (SCD Type 2)
    await upsertAgentDimension(agentId, parsedEvent);
    
    // Insert fact_agent_session start record
    await insertAgentSessionStart(agentId, parsedEvent, timestamp);
}

async function processStateChange(agentId, newState, parsedEvent, timestamp) {
    const session = await getAgentSession(agentId);
    if (!session) return;
    
    // End previous state in Redshift
    await endAgentState(agentId, session.currentState, timestamp);
    
    // Start new state in Redshift
    await startAgentState(agentId, newState, parsedEvent, timestamp);
    
    // Update DynamoDB session
    await dynamodb.update({
        TableName: AGENT_SESSIONS_TABLE,
        Key: { tenantId: TENANT_ID, agentId },
        UpdateExpression: 'SET currentState = :state, currentStateStart = :start',
        ExpressionAttributeValues: {
            ':state': newState,
            ':start': timestamp
        }
    }).promise();
}
```

### 5. Scheduled ETL Processing (Per Tenant)

#### ETL Transformer Lambda
```javascript
// brandon911-etl-transformer Lambda (runs every 5 minutes)
const TENANT_ID = process.env.TENANT_ID;
const REDSHIFT_SCHEMA = `${TENANT_ID}_reporting`;

exports.handler = async (event) => {
    console.log(`Running ETL for tenant: ${TENANT_ID}`);
    
    try {
        // Transform callsummary to fact_call
        await transformCallsummaryToFacts();
        
        // Update dimensions from call data
        await updateDimensionsFromCalls();
        
        // Trigger interval aggregation
        await triggerIntervalAggregation();
        
        return { success: true, tenant: TENANT_ID };
    } catch (error) {
        console.error('ETL error:', error);
        throw error;
    }
};

async function transformCallsummaryToFacts() {
    // Call stored procedure to transform raw callsummary data
    const sql = `CALL ${REDSHIFT_SCHEMA}.sp_transform_callsummary_to_facts('${TENANT_ID}');`;
    
    await redshift.executeStatement({
        ClusterIdentifier: process.env.REDSHIFT_CLUSTER,
        Database: process.env.REDSHIFT_DATABASE,
        Sql: sql
    }).promise();
}
```

### 6. Interval Aggregation (Per Tenant)

#### Interval Aggregator Lambda
```javascript
// brandon911-interval-aggregator Lambda (runs every 5 minutes)
exports.handler = async (event) => {
    const currentInterval = getCurrentInterval();
    
    // Calculate agent intervals
    await calculateAgentIntervals(currentInterval);
    
    // Calculate queue intervals
    await calculateQueueIntervals(currentInterval);
};

async function calculateAgentIntervals(intervalStart) {
    const intervalEnd = new Date(intervalStart.getTime() + 5 * 60 * 1000);
    
    const sql = `
        INSERT INTO ${REDSHIFT_SCHEMA}.fact_agent_intervals (
            tenant_id, agent_id, queue_id, interval_id,
            interval_start, interval_end,
            staffed_time_seconds, available_time_seconds, 
            wrapup_time_seconds, talk_time_seconds,
            calls_answered, group_utilization_pct, available_time_pct
        )
        SELECT 
            s.tenant_id,
            s.agent_id,
            s.queue_id,
            ti.interval_id,
            '${intervalStart.toISOString()}',
            '${intervalEnd.toISOString()}',
            -- Staffed time: session duration within interval
            COALESCE(SUM(
                CASE WHEN s.logout_timestamp IS NULL 
                THEN EXTRACT(EPOCH FROM '${intervalEnd.toISOString()}'::timestamp - GREATEST(s.login_timestamp, '${intervalStart.toISOString()}'::timestamp))
                ELSE EXTRACT(EPOCH FROM LEAST(s.logout_timestamp, '${intervalEnd.toISOString()}'::timestamp) - GREATEST(s.login_timestamp, '${intervalStart.toISOString()}'::timestamp))
                END
            ), 0) as staffed_time_seconds,
            -- Available time: sum of AVAILABLE state durations
            COALESCE(SUM(
                CASE WHEN st.state_type = 'AVAILABLE'
                THEN EXTRACT(EPOCH FROM LEAST(COALESCE(st.state_end_time, '${intervalEnd.toISOString()}'::timestamp), '${intervalEnd.toISOString()}'::timestamp) - GREATEST(st.state_start_time, '${intervalStart.toISOString()}'::timestamp))
                ELSE 0 END
            ), 0) as available_time_seconds,
            -- Wrap-up time
            COALESCE(SUM(
                CASE WHEN st.state_type = 'BUSY_WRAPUP'
                THEN EXTRACT(EPOCH FROM LEAST(COALESCE(st.state_end_time, '${intervalEnd.toISOString()}'::timestamp), '${intervalEnd.toISOString()}'::timestamp) - GREATEST(st.state_start_time, '${intervalStart.toISOString()}'::timestamp))
                ELSE 0 END
            ), 0) as wrapup_time_seconds,
            -- Talk time from calls
            COALESCE(SUM(c.talk_time_sec), 0) as talk_time_seconds,
            -- Calls answered
            COUNT(c.call_fact_id) as calls_answered,
            -- Group utilization percentage
            CASE WHEN SUM(staffed_time_seconds) > 0 
            THEN (SUM(talk_time_seconds) / SUM(staffed_time_seconds)) * 100 
            ELSE 0 END as group_utilization_pct,
            -- Available time percentage  
            CASE WHEN SUM(staffed_time_seconds) > 0
            THEN (SUM(available_time_seconds) / SUM(staffed_time_seconds)) * 100
            ELSE 0 END as available_time_pct
        FROM ${REDSHIFT_SCHEMA}.fact_agent_session s
        LEFT JOIN ${REDSHIFT_SCHEMA}.fact_agent_state st ON s.agent_id = st.agent_id 
            AND st.state_start_time < '${intervalEnd.toISOString()}'
            AND COALESCE(st.state_end_time, '${intervalEnd.toISOString()}') > '${intervalStart.toISOString()}'
        LEFT JOIN ${REDSHIFT_SCHEMA}.fact_call c ON s.agent_id = c.agent_id
            AND c.start_timestamp >= '${intervalStart.toISOString()}'
            AND c.start_timestamp < '${intervalEnd.toISOString()}'
        LEFT JOIN ${REDSHIFT_SCHEMA}.dim_time_interval ti ON ti.interval_start = '${intervalStart.toISOString()}'
        WHERE s.tenant_id = '${TENANT_ID}'
            AND (s.login_timestamp < '${intervalEnd.toISOString()}' 
                 AND COALESCE(s.logout_timestamp, '${intervalEnd.toISOString()}') > '${intervalStart.toISOString()}')
        GROUP BY s.tenant_id, s.agent_id, s.queue_id, ti.interval_id
        HAVING SUM(staffed_time_seconds) > 0;
    `;
    
    await redshift.executeStatement({
        ClusterIdentifier: process.env.REDSHIFT_CLUSTER,
        Database: process.env.REDSHIFT_DATABASE,
        Sql: sql
    }).promise();
}

function getCurrentInterval() {
    const now = new Date();
    const minutes = Math.floor(now.getMinutes() / 5) * 5;
    return new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), minutes, 0, 0);
}
```

### 7. Redshift Stored Procedures (Required for ETL)

#### Why Stored Procedures Are Needed
- **Complex Transformations**: Convert raw callsummary data to fact_call with business logic
- **SCD Type 2 Management**: Handle slowly changing dimensions for agents/queues
- **Data Quality**: Handle missing data, duplicates, and orphaned records
- **Performance**: Bulk operations are faster than individual Lambda calls
- **Atomicity**: Ensure data consistency during transformations

#### Key Stored Procedures

```sql
-- Transform callsummary to fact_call
CREATE OR REPLACE PROCEDURE brandon911_reporting.sp_transform_callsummary_to_facts(
    p_tenant_id VARCHAR(50)
)
AS $$
BEGIN
    -- Insert new fact_call records from raw_callsummary
    INSERT INTO brandon911_reporting.fact_call (
        tenant_id, agent_id, queue_id, call_type_id,
        start_time_id, end_time_id, call_identifier,
        start_timestamp, end_timestamp, answer_timestamp,
        talk_time_sec, total_call_sec, ring_time_sec,
        abandoned_flag, transferred_in_flag, transferred_out_flag,
        answered_le_10s_flag, answered_le_15s_flag,
        answered_le_20s_flag, answered_le_40s_flag,
        source_system, created_at
    )
    SELECT
        cs.tenant_id,
        COALESCE(da.agent_id, -1) as agent_id,
        COALESCE(dq.queue_id, -1) as queue_id,
        COALESCE(dct.call_type_id, 1) as call_type_id,
        dt_start.time_id as start_time_id,
        dt_end.time_id as end_time_id,
        cs.call_identifier,
        cs.start_timestamp,
        cs.end_timestamp,
        cs.answer_timestamp,
        cs.talk_time_sec,
        cs.total_call_sec,
        cs.ring_time_sec,
        cs.abandoned_flag,
        cs.transferred_in_flag,
        cs.transferred_out_flag,
        cs.answered_le_10s_flag,
        cs.answered_le_15s_flag,
        cs.answered_le_20s_flag,
        cs.answered_le_40s_flag,
        'collector_api',
        GETDATE()
    FROM brandon911_reporting.raw_callsummary cs
    LEFT JOIN brandon911_reporting.dim_agent da ON cs.agent = da.agent_name
        AND da.tenant_id = p_tenant_id AND da.is_current = true
    LEFT JOIN brandon911_reporting.dim_queue dq ON cs.queue = dq.queue_name
        AND dq.tenant_id = p_tenant_id AND dq.is_current = true
    LEFT JOIN brandon911_reporting.dim_call_type dct ON cs.call_type = dct.call_type_name
    LEFT JOIN brandon911_reporting.dim_time dt_start ON DATE(cs.start_timestamp) = dt_start.full_date
    LEFT JOIN brandon911_reporting.dim_time dt_end ON DATE(cs.end_timestamp) = dt_end.full_date
    WHERE cs.tenant_id = p_tenant_id
        AND cs.processed_flag = false;

    -- Mark records as processed
    UPDATE brandon911_reporting.raw_callsummary
    SET processed_flag = true, processed_at = GETDATE()
    WHERE tenant_id = p_tenant_id AND processed_flag = false;

    GET DIAGNOSTICS result_count = ROW_COUNT;
    RAISE NOTICE 'Processed % call records for tenant %', result_count, p_tenant_id;
END;
$$ LANGUAGE plpgsql;

-- Upsert agent dimension with SCD Type 2
CREATE OR REPLACE PROCEDURE brandon911_reporting.sp_upsert_agent(
    p_tenant_id VARCHAR(50),
    p_agent_name VARCHAR(255),
    p_agent_role VARCHAR(100),
    p_agent_uri VARCHAR(255),
    p_operator_id VARCHAR(100),
    p_workstation VARCHAR(255),
    p_device_name VARCHAR(100),
    p_tenant_group VARCHAR(100)
)
AS $$
DECLARE
    v_existing_agent_id INT;
    v_changes_detected BOOLEAN := false;
BEGIN
    -- Check for existing current record
    SELECT agent_id INTO v_existing_agent_id
    FROM brandon911_reporting.dim_agent
    WHERE tenant_id = p_tenant_id
        AND agent_name = p_agent_name
        AND is_current = true;

    IF v_existing_agent_id IS NOT NULL THEN
        -- Check if any attributes changed
        SELECT COUNT(*) > 0 INTO v_changes_detected
        FROM brandon911_reporting.dim_agent
        WHERE agent_id = v_existing_agent_id
            AND (agent_role != p_agent_role
                 OR agent_uri != p_agent_uri
                 OR operator_id != p_operator_id
                 OR workstation != p_workstation
                 OR device_name != p_device_name
                 OR tenant_group != p_tenant_group);

        IF v_changes_detected THEN
            -- End current record
            UPDATE brandon911_reporting.dim_agent
            SET valid_to = GETDATE(), is_current = false
            WHERE agent_id = v_existing_agent_id;

            -- Insert new current record
            INSERT INTO brandon911_reporting.dim_agent (
                tenant_id, agent_name, agent_role, agent_uri,
                operator_id, workstation, device_name, tenant_group,
                valid_from, valid_to, is_current
            ) VALUES (
                p_tenant_id, p_agent_name, p_agent_role, p_agent_uri,
                p_operator_id, p_workstation, p_device_name, p_tenant_group,
                GETDATE(), '9999-12-31', true
            );
        END IF;
    ELSE
        -- Insert new agent
        INSERT INTO brandon911_reporting.dim_agent (
            tenant_id, agent_name, agent_role, agent_uri,
            operator_id, workstation, device_name, tenant_group,
            valid_from, valid_to, is_current
        ) VALUES (
            p_tenant_id, p_agent_name, p_agent_role, p_agent_uri,
            p_operator_id, p_workstation, p_device_name, p_tenant_group,
            GETDATE(), '9999-12-31', true
        );
    END IF;
END;
$$ LANGUAGE plpgsql;
```

### 8. Power BI Integration & Queries

#### Power BI Data Source Configuration
```sql
-- Power BI connects to Redshift with tenant-specific views
-- Connection: redshift-cluster.region.redshift.amazonaws.com:5439/reporting
-- Schema: brandon911_reporting

-- View for ACD Detailed Calls by Group Report
CREATE VIEW brandon911_reporting.view_acd_detailed_calls AS
SELECT
    dq.queue_name as "Ring Group",
    COUNT(fc.call_fact_id) as "Calls Answered",
    SUM(CASE WHEN fc.transferred_in_flag THEN 1 ELSE 0 END) as "Transferred In",
    SUM(CASE WHEN fc.transferred_out_flag THEN 1 ELSE 0 END) as "Transferred Out",

    -- Staffed Time (hours) - from agent sessions
    COALESCE(SUM(fas.session_duration_sec) / 3600.0, 0) as "Staffed Time",

    -- Available Time (hours) - from agent states
    COALESCE(SUM(CASE WHEN fast.state_type = 'AVAILABLE'
                 THEN fast.state_duration_sec ELSE 0 END) / 3600.0, 0) as "Available Time",

    -- Wrap-up Time (minutes) - from agent states
    COALESCE(SUM(CASE WHEN fast.state_type = 'BUSY_WRAPUP'
                 THEN fast.state_duration_sec ELSE 0 END) / 60.0, 0) as "Wrap-up Time",

    -- Talk Time (hours) - from calls
    COALESCE(SUM(fc.talk_time_sec) / 3600.0, 0) as "Talk Time",

    -- Handling Time (hours) - total call time
    COALESCE(SUM(fc.total_call_sec) / 3600.0, 0) as "Handling Time",

    -- Agents Logged In - distinct agents with sessions
    STRING_AGG(DISTINCT da.agent_name, ', ') as "Agents Logged In",

    -- Service Level - percentage answered within 10s
    CASE WHEN COUNT(fc.call_fact_id) > 0
    THEN AVG(CASE WHEN fc.answered_le_10s_flag THEN 100.0 ELSE 0.0 END)
    ELSE 0 END as "Service Level %"

FROM brandon911_reporting.dim_queue dq
LEFT JOIN brandon911_reporting.fact_call fc ON dq.queue_id = fc.queue_id
LEFT JOIN brandon911_reporting.fact_agent_session fas ON dq.queue_id = fas.queue_id
LEFT JOIN brandon911_reporting.fact_agent_state fast ON fas.agent_id = fast.agent_id
    AND fast.queue_id = dq.queue_id
LEFT JOIN brandon911_reporting.dim_agent da ON fas.agent_id = da.agent_id
    AND da.is_current = true
WHERE dq.tenant_id = 'brandon911'
    AND dq.is_current = true
    AND fc.start_timestamp >= DATEADD(day, -1, GETDATE()) -- Last 24 hours
GROUP BY dq.queue_name
ORDER BY dq.queue_name;

-- View for Call Queue Summary Report (Dashboard)
CREATE VIEW brandon911_reporting.view_queue_summary AS
SELECT
    dq.queue_name as "Queue Name",
    COUNT(fc.call_fact_id) as "Calls",
    AVG(CASE WHEN fc.answered_le_10s_flag THEN 100.0 ELSE 0.0 END) as "Calls Answered in 10s%",
    SUM(CASE WHEN fc.abandoned_flag THEN 1 ELSE 0 END) as "Abandoned",
    SUM(fas.session_duration_sec) / 3600.0 as "Logged in Time",
    COUNT(DISTINCT fas.agent_id) as "Agents Logged In",

    -- Group Utilization% = (Talk Time / Staffed Time) * 100
    CASE WHEN SUM(fas.session_duration_sec) > 0
    THEN (SUM(fc.talk_time_sec) / SUM(fas.session_duration_sec)) * 100
    ELSE 0 END as "Group Utilization%"

FROM brandon911_reporting.dim_queue dq
LEFT JOIN brandon911_reporting.fact_call fc ON dq.queue_id = fc.queue_id
LEFT JOIN brandon911_reporting.fact_agent_session fas ON dq.queue_id = fas.queue_id
WHERE dq.tenant_id = 'brandon911'
    AND dq.is_current = true
    AND fc.start_timestamp >= DATEADD(hour, -1, GETDATE()) -- Last hour
GROUP BY dq.queue_name
ORDER BY "Calls" DESC;

-- View for Call Taking Group Overview (Dashboard)
CREATE VIEW brandon911_reporting.view_call_taking_overview AS
SELECT
    dq.queue_name as "ACD Group",
    COUNT(fc.call_fact_id) as "Calls",
    SUM(CASE WHEN fc.answered_le_10s_flag THEN 1 ELSE 0 END) as "Calls Answered Within 10s",
    SUM(CASE WHEN fc.answered_le_15s_flag THEN 1 ELSE 0 END) as "Calls Answered Within 15s",
    SUM(CASE WHEN fc.answered_le_20s_flag THEN 1 ELSE 0 END) as "Calls Answered Within 20s",
    SUM(CASE WHEN fc.answered_le_40s_flag THEN 1 ELSE 0 END) as "Calls Answered Within 40s",
    SUM(CASE WHEN fc.abandoned_flag THEN 1 ELSE 0 END) as "Calls Abandoned",
    SUM(CASE WHEN fc.transferred_in_flag OR fc.transferred_out_flag THEN 1 ELSE 0 END) as "Calls Transferred",

    -- Service Level %
    AVG(CASE WHEN fc.answered_le_10s_flag THEN 100.0 ELSE 0.0 END) as "Service Level %",

    -- Logged in Time (minutes)
    SUM(fas.session_duration_sec) / 60.0 as "Logged in Time",

    -- Available Time (minutes)
    SUM(CASE WHEN fast.state_type = 'AVAILABLE'
        THEN fast.state_duration_sec ELSE 0 END) / 60.0 as "Available Time",

    -- Group Utilization% = (Available Time / Logged in Time) * 100
    CASE WHEN SUM(fas.session_duration_sec) > 0
    THEN (SUM(CASE WHEN fast.state_type = 'AVAILABLE' THEN fast.state_duration_sec ELSE 0 END)
          / SUM(fas.session_duration_sec)) * 100
    ELSE 0 END as "Group Utilization%"

FROM brandon911_reporting.dim_queue dq
LEFT JOIN brandon911_reporting.fact_call fc ON dq.queue_id = fc.queue_id
LEFT JOIN brandon911_reporting.fact_agent_session fas ON dq.queue_id = fas.queue_id
LEFT JOIN brandon911_reporting.fact_agent_state fast ON fas.agent_id = fast.agent_id
WHERE dq.tenant_id = 'brandon911'
    AND dq.is_current = true
    AND fc.start_timestamp >= DATEADD(hour, -4, GETDATE()) -- Last 4 hours
GROUP BY dq.queue_name
ORDER BY "Calls" DESC;
```

### 9. Scheduled Processing Summary

#### Required Timers & Why

1. **ETL Transformer (Every 5 minutes)**
   - **Purpose**: Transform raw callsummary → fact_call
   - **Why Needed**: Collector API writes to raw tables, need business logic transformation
   - **Trigger**: CloudWatch Events Rule

2. **Interval Aggregator (Every 5 minutes)**
   - **Purpose**: Calculate fact_agent_intervals and fact_queue_intervals
   - **Why Needed**: Power BI needs pre-aggregated data for performance
   - **Trigger**: CloudWatch Events Rule

3. **Dimension Maintenance (Hourly)**
   - **Purpose**: Clean up old SCD Type 2 records, update statistics
   - **Why Needed**: Keep dimension tables optimized
   - **Trigger**: CloudWatch Events Rule

#### No Real-time Processing Needed Because:
- **Agent Events**: Processed immediately by Lambda → Redshift
- **Call Events**: Processed immediately by Collector API → MariaDB → Redshift
- **Intervals**: 5-minute aggregation is sufficient for "near real-time" dashboards

### 10. Tenant Isolation Summary

#### Completely Isolated Resources (Per Tenant):
- ✅ **SNS Topics**: `i3-events-{tenant}`
- ✅ **Lambda Functions**: `{tenant}-*-processor`
- ✅ **DynamoDB Tables**: `{tenant}-agent-sessions`
- ✅ **Redshift Schemas**: `{tenant}_reporting`
- ✅ **Power BI Workspaces**: Separate workspace per tenant

#### Shared Resources (Multi-tenant):
- ⚠️ **Event Router Lambda**: Shared but tenant-aware
- ⚠️ **Collector API**: Shared EC2 with tenant isolation
- ⚠️ **MariaDB**: Shared database with tenant_id column
- ⚠️ **Redshift Cluster**: Shared cluster with schema isolation

This architecture provides **99% tenant isolation** while maintaining cost efficiency through shared infrastructure for proven components.
```
