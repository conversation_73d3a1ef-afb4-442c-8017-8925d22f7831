-- =====================================================
-- Redshift Data Warehouse Schema - Dimension Tables
-- =====================================================

-- Create schema if not exists
CREATE SCHEMA IF NOT EXISTS reporting;

-- =====================================================
-- DIMENSION TABLES
-- =====================================================

-- Tenant Dimension (SCD Type 2)
CREATE TABLE IF NOT EXISTS reporting.dim_tenant (
    tenant_id INTEGER IDENTITY(1,1) PRIMARY KEY,
    tenant_psap_name VARCHAR(255) NOT NULL,
    timezone_name VARCHAR(100) NOT NULL,
    valid_from TIMESTAMP NOT NULL,
    valid_to TIMESTAMP,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT GETDATE(),
    updated_at TIMESTAMP DEFAULT GETDATE()
)
DISTSTYLE KEY
DISTKEY (tenant_id)
SORTKEY (tenant_psap_name, valid_from);

-- Agent Dimension (SCD Type 2)
CREATE TABLE IF NOT EXISTS reporting.dim_agent (
    agent_id INTEGER IDENTITY(1,1) PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    agent_name VARCHAR(255) NOT NULL,
    agent_role VARCHAR(100),
    agent_uri VARCHAR(255),
    operator_id VARCHAR(100),
    workstation VARCHAR(255),
    valid_from TIMESTAMP NOT NULL,
    valid_to TIMESTAMP,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT GETDATE(),
    updated_at TIMESTAMP DEFAULT GETDATE(),
    FOREIGN KEY (tenant_id) REFERENCES reporting.dim_tenant(tenant_id)
)
DISTSTYLE KEY
DISTKEY (tenant_id)
SORTKEY (tenant_id, agent_name, valid_from);

-- Queue Dimension (SCD Type 2)
CREATE TABLE IF NOT EXISTS reporting.dim_queue (
    queue_id INTEGER IDENTITY(1,1) PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    queue_name VARCHAR(255) NOT NULL,
    ring_group_name VARCHAR(255),
    ring_group_uri VARCHAR(255),
    valid_from TIMESTAMP NOT NULL,
    valid_to TIMESTAMP,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT GETDATE(),
    updated_at TIMESTAMP DEFAULT GETDATE(),
    FOREIGN KEY (tenant_id) REFERENCES reporting.dim_tenant(tenant_id)
)
DISTSTYLE KEY
DISTKEY (tenant_id)
SORTKEY (tenant_id, queue_name, valid_from);

-- Time Dimension
CREATE TABLE IF NOT EXISTS reporting.dim_time (
    time_id INTEGER PRIMARY KEY,
    full_date DATE NOT NULL,
    year SMALLINT NOT NULL,
    month SMALLINT NOT NULL,
    day SMALLINT NOT NULL,
    day_of_week VARCHAR(20) NOT NULL,
    is_weekend BOOLEAN NOT NULL,
    is_business_hour BOOLEAN NOT NULL,
    hour SMALLINT NOT NULL,
    minute SMALLINT NOT NULL,
    created_at TIMESTAMP DEFAULT GETDATE()
)
DISTSTYLE ALL
SORTKEY (time_id);

-- Call Type Dimension
CREATE TABLE IF NOT EXISTS reporting.dim_call_type (
    call_type_id INTEGER PRIMARY KEY,
    call_type_name VARCHAR(50) NOT NULL,
    is_emergency BOOLEAN NOT NULL DEFAULT FALSE,
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    is_outbound BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT GETDATE()
)
DISTSTYLE ALL
SORTKEY (call_type_id);

-- Reason Code Dimension
CREATE TABLE IF NOT EXISTS reporting.dim_reason_code (
    reason_id INTEGER IDENTITY(1,1) PRIMARY KEY,
    reason_code VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(255),
    category VARCHAR(50), -- Break/Training/Unavailable/WrapUp
    created_at TIMESTAMP DEFAULT GETDATE()
)
DISTSTYLE ALL
SORTKEY (reason_code);

-- =====================================================
-- POPULATE STATIC DIMENSIONS
-- =====================================================

-- Populate Call Types
INSERT INTO reporting.dim_call_type (call_type_id, call_type_name, is_emergency, is_admin, is_outbound)
VALUES 
    (1, 'Emergency', TRUE, FALSE, FALSE),
    (2, 'Admin', FALSE, TRUE, FALSE),
    (3, 'Outbound', FALSE, FALSE, TRUE),
    (4, 'Unknown', FALSE, FALSE, FALSE)
ON CONFLICT (call_type_id) DO NOTHING;

-- Populate Common Reason Codes
INSERT INTO reporting.dim_reason_code (reason_code, description, category)
VALUES 
    ('BREAK', 'Break Time', 'Break'),
    ('TRAINING', 'Training Session', 'Training'),
    ('UNAVAILABLE', 'Unavailable', 'Unavailable'),
    ('WRAPUP_FIXED', 'Wrap Up - Fixed Duration', 'WrapUp'),
    ('WRAPUP_VARIABLE', 'Wrap Up - Variable Duration', 'WrapUp'),
    ('MANUAL', 'Manual Busy Out', 'Unavailable')
ON CONFLICT (reason_code) DO NOTHING;

-- =====================================================
-- DIMENSION MANAGEMENT PROCEDURES
-- =====================================================

-- Procedure to upsert tenant dimension
CREATE OR REPLACE PROCEDURE reporting.upsert_tenant(
    p_tenant_psap_name VARCHAR(255),
    p_timezone_name VARCHAR(100)
)
AS $$
DECLARE
    v_tenant_id INTEGER;
BEGIN
    -- Check if tenant exists and is current
    SELECT tenant_id INTO v_tenant_id
    FROM reporting.dim_tenant
    WHERE tenant_psap_name = p_tenant_psap_name
      AND is_current = TRUE;
    
    IF v_tenant_id IS NULL THEN
        -- Insert new tenant
        INSERT INTO reporting.dim_tenant (
            tenant_psap_name, timezone_name, valid_from, is_current
        ) VALUES (
            p_tenant_psap_name, p_timezone_name, GETDATE(), TRUE
        );
    ELSE
        -- Update existing tenant if timezone changed
        UPDATE reporting.dim_tenant
        SET timezone_name = p_timezone_name,
            updated_at = GETDATE()
        WHERE tenant_id = v_tenant_id
          AND timezone_name != p_timezone_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Procedure to upsert agent dimension
CREATE OR REPLACE PROCEDURE reporting.upsert_agent(
    p_tenant_id INTEGER,
    p_agent_name VARCHAR(255),
    p_agent_role VARCHAR(100),
    p_agent_uri VARCHAR(255),
    p_operator_id VARCHAR(100),
    p_workstation VARCHAR(255)
)
AS $$
DECLARE
    v_agent_id INTEGER;
    v_changed BOOLEAN := FALSE;
BEGIN
    -- Check if agent exists and is current
    SELECT agent_id INTO v_agent_id
    FROM reporting.dim_agent
    WHERE tenant_id = p_tenant_id
      AND agent_name = p_agent_name
      AND is_current = TRUE;
    
    IF v_agent_id IS NULL THEN
        -- Insert new agent
        INSERT INTO reporting.dim_agent (
            tenant_id, agent_name, agent_role, agent_uri, 
            operator_id, workstation, valid_from, is_current
        ) VALUES (
            p_tenant_id, p_agent_name, p_agent_role, p_agent_uri,
            p_operator_id, p_workstation, GETDATE(), TRUE
        );
    ELSE
        -- Check if any attributes changed
        SELECT CASE WHEN 
            COALESCE(agent_role, '') != COALESCE(p_agent_role, '') OR
            COALESCE(agent_uri, '') != COALESCE(p_agent_uri, '') OR
            COALESCE(operator_id, '') != COALESCE(p_operator_id, '') OR
            COALESCE(workstation, '') != COALESCE(p_workstation, '')
        THEN TRUE ELSE FALSE END
        INTO v_changed
        FROM reporting.dim_agent
        WHERE agent_id = v_agent_id;
        
        IF v_changed THEN
            -- Close current record
            UPDATE reporting.dim_agent
            SET valid_to = GETDATE(),
                is_current = FALSE,
                updated_at = GETDATE()
            WHERE agent_id = v_agent_id;
            
            -- Insert new record
            INSERT INTO reporting.dim_agent (
                tenant_id, agent_name, agent_role, agent_uri,
                operator_id, workstation, valid_from, is_current
            ) VALUES (
                p_tenant_id, p_agent_name, p_agent_role, p_agent_uri,
                p_operator_id, p_workstation, GETDATE(), TRUE
            );
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Procedure to upsert queue dimension
CREATE OR REPLACE PROCEDURE reporting.upsert_queue(
    p_tenant_id INTEGER,
    p_queue_name VARCHAR(255),
    p_ring_group_name VARCHAR(255),
    p_ring_group_uri VARCHAR(255)
)
AS $$
DECLARE
    v_queue_id INTEGER;
    v_changed BOOLEAN := FALSE;
BEGIN
    -- Check if queue exists and is current
    SELECT queue_id INTO v_queue_id
    FROM reporting.dim_queue
    WHERE tenant_id = p_tenant_id
      AND queue_name = p_queue_name
      AND is_current = TRUE;

    IF v_queue_id IS NULL THEN
        -- Insert new queue
        INSERT INTO reporting.dim_queue (
            tenant_id, queue_name, ring_group_name, ring_group_uri,
            valid_from, is_current
        ) VALUES (
            p_tenant_id, p_queue_name, p_ring_group_name, p_ring_group_uri,
            GETDATE(), TRUE
        );
    ELSE
        -- Check if any attributes changed
        SELECT CASE WHEN
            COALESCE(ring_group_name, '') != COALESCE(p_ring_group_name, '') OR
            COALESCE(ring_group_uri, '') != COALESCE(p_ring_group_uri, '')
        THEN TRUE ELSE FALSE END
        INTO v_changed
        FROM reporting.dim_queue
        WHERE queue_id = v_queue_id;

        IF v_changed THEN
            -- Close current record
            UPDATE reporting.dim_queue
            SET valid_to = GETDATE(),
                is_current = FALSE,
                updated_at = GETDATE()
            WHERE queue_id = v_queue_id;

            -- Insert new record
            INSERT INTO reporting.dim_queue (
                tenant_id, queue_name, ring_group_name, ring_group_uri,
                valid_from, is_current
            ) VALUES (
                p_tenant_id, p_queue_name, p_ring_group_name, p_ring_group_uri,
                GETDATE(), TRUE
            );
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql;
