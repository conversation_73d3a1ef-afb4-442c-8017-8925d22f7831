const AWS = require('aws-sdk');

const dynamodb = new AWS.DynamoDB.DocumentClient();
const redshift = new AWS.RedshiftData();

const AGENT_SESSIONS_TABLE = process.env.AGENT_SESSIONS_TABLE;
const REDSHIFT_CLUSTER = process.env.REDSHIFT_CLUSTER;
const REDSHIFT_DATABASE = process.env.REDSHIFT_DATABASE;

exports.handler = async (event) => {
    console.log('Agent Event Processor received:', JSON.stringify(event, null, 2));
    
    const results = [];
    
    for (const record of event.Records) {
        try {
            const message = JSON.parse(record.body);
            const result = await processAgentEvent(message);
            results.push(result);
        } catch (error) {
            console.error('Error processing agent event:', error);
            results.push({ success: false, error: error.message });
        }
    }
    
    return {
        statusCode: 200,
        body: JSON.stringify({
            processed: results.length,
            results: results
        })
    };
};

async function processAgentEvent(message) {
    const { tenantId, eventType, parsedEvent, timestamp } = message;
    const agentId = parsedEvent.agent || parsedEvent.agentName;
    
    console.log(`Processing ${eventType} for agent ${agentId}, tenant ${tenantId}`);
    
    switch (eventType) {
        case 'Login':
            return await processLogin(tenantId, agentId, parsedEvent, timestamp);
        case 'Logout':
            return await processLogout(tenantId, agentId, parsedEvent, timestamp);
        case 'ACDLogin':
            return await processACDLogin(tenantId, agentId, parsedEvent, timestamp);
        case 'ACDLogout':
            return await processACDLogout(tenantId, agentId, parsedEvent, timestamp);
        case 'Available':
            return await processAvailable(tenantId, agentId, parsedEvent, timestamp);
        case 'BusiedOut':
            return await processBusiedOut(tenantId, agentId, parsedEvent, timestamp);
        default:
            console.log(`Unknown agent event type: ${eventType}`);
            return { success: true, eventType, action: 'ignored' };
    }
}

async function processLogin(tenantId, agentId, parsedEvent, timestamp) {
    // End any existing session first
    await endExistingSession(tenantId, agentId, timestamp);
    
    // Create new session
    const sessionData = {
        tenantId: tenantId,
        agentId: agentId,
        sessionStart: timestamp,
        currentState: 'LOGGED_IN',
        currentStateStart: timestamp,
        device: parsedEvent.device,
        workstation: parsedEvent.workstation,
        queueAssignments: [],
        stateHistory: [{
            state: 'LOGGED_IN',
            startTime: timestamp,
            duration: null
        }],
        intervalMetrics: {},
        ttl: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days TTL
        createdAt: new Date().toISOString()
    };
    
    await dynamodb.put({
        TableName: AGENT_SESSIONS_TABLE,
        Item: sessionData
    }).promise();
    
    // Upsert agent dimension
    await upsertAgentDimension(tenantId, agentId, parsedEvent);
    
    return { 
        success: true, 
        eventType: 'Login', 
        agentId, 
        action: 'session_started' 
    };
}

async function processLogout(tenantId, agentId, parsedEvent, timestamp) {
    const session = await getAgentSession(tenantId, agentId);
    
    if (!session) {
        console.log(`No active session found for agent ${agentId}`);
        return { success: true, eventType: 'Logout', agentId, action: 'no_session' };
    }
    
    // Calculate final state duration
    const currentStateDuration = Math.floor(
        (new Date(timestamp) - new Date(session.currentStateStart)) / 1000
    );
    
    // Update final state in history
    const finalStateHistory = [...session.stateHistory];
    if (finalStateHistory.length > 0) {
        finalStateHistory[finalStateHistory.length - 1].duration = currentStateDuration;
        finalStateHistory[finalStateHistory.length - 1].endTime = timestamp;
    }
    
    // Calculate session totals
    const sessionDuration = Math.floor(
        (new Date(timestamp) - new Date(session.sessionStart)) / 1000
    );
    
    // Update session with logout info
    await dynamodb.update({
        TableName: AGENT_SESSIONS_TABLE,
        Key: { tenantId, agentId },
        UpdateExpression: `SET 
            sessionEnd = :sessionEnd,
            currentState = :state,
            sessionDuration = :duration,
            stateHistory = :history,
            updatedAt = :updatedAt`,
        ExpressionAttributeValues: {
            ':sessionEnd': timestamp,
            ':state': 'LOGGED_OUT',
            ':duration': sessionDuration,
            ':history': finalStateHistory,
            ':updatedAt': new Date().toISOString()
        }
    }).promise();
    
    // Create fact records in Redshift
    await createAgentSessionFact(session, timestamp, sessionDuration);
    await createAgentStateFacts(session, finalStateHistory);
    await updateAgentIntervals(session, timestamp);
    
    return { 
        success: true, 
        eventType: 'Logout', 
        agentId, 
        sessionDuration,
        action: 'session_ended' 
    };
}

async function processAvailable(tenantId, agentId, parsedEvent, timestamp) {
    const session = await getAgentSession(tenantId, agentId);
    
    if (!session) {
        console.log(`No active session found for agent ${agentId}`);
        return { success: true, eventType: 'Available', agentId, action: 'no_session' };
    }
    
    // Calculate previous state duration
    const previousStateDuration = Math.floor(
        (new Date(timestamp) - new Date(session.currentStateStart)) / 1000
    );
    
    // Update state history
    const updatedHistory = [...session.stateHistory];
    if (updatedHistory.length > 0) {
        updatedHistory[updatedHistory.length - 1].duration = previousStateDuration;
        updatedHistory[updatedHistory.length - 1].endTime = timestamp;
    }
    
    // Add new state
    updatedHistory.push({
        state: 'AVAILABLE',
        startTime: timestamp,
        duration: null,
        queueId: parsedEvent.queueId
    });
    
    // Update session
    await dynamodb.update({
        TableName: AGENT_SESSIONS_TABLE,
        Key: { tenantId, agentId },
        UpdateExpression: `SET 
            currentState = :state,
            currentStateStart = :stateStart,
            stateHistory = :history,
            updatedAt = :updatedAt`,
        ExpressionAttributeValues: {
            ':state': 'AVAILABLE',
            ':stateStart': timestamp,
            ':history': updatedHistory,
            ':updatedAt': new Date().toISOString()
        }
    }).promise();
    
    // Update 5-minute interval metrics
    await updateIntervalMetrics(tenantId, agentId, session.currentState, previousStateDuration, timestamp);
    
    return { 
        success: true, 
        eventType: 'Available', 
        agentId, 
        previousStateDuration,
        action: 'state_changed' 
    };
}

async function processBusiedOut(tenantId, agentId, parsedEvent, timestamp) {
    const session = await getAgentSession(tenantId, agentId);
    
    if (!session) {
        console.log(`No active session found for agent ${agentId}`);
        return { success: true, eventType: 'BusiedOut', agentId, action: 'no_session' };
    }
    
    // Calculate previous state duration
    const previousStateDuration = Math.floor(
        (new Date(timestamp) - new Date(session.currentStateStart)) / 1000
    );
    
    // Determine busy state type
    const busyState = getBusyStateType(parsedEvent.action);
    
    // Update state history
    const updatedHistory = [...session.stateHistory];
    if (updatedHistory.length > 0) {
        updatedHistory[updatedHistory.length - 1].duration = previousStateDuration;
        updatedHistory[updatedHistory.length - 1].endTime = timestamp;
    }
    
    // Add new busy state
    updatedHistory.push({
        state: busyState,
        startTime: timestamp,
        duration: null,
        action: parsedEvent.action,
        reasonCode: parsedEvent.reasonCode
    });
    
    // Update session
    await dynamodb.update({
        TableName: AGENT_SESSIONS_TABLE,
        Key: { tenantId, agentId },
        UpdateExpression: `SET 
            currentState = :state,
            currentStateStart = :stateStart,
            stateHistory = :history,
            updatedAt = :updatedAt`,
        ExpressionAttributeValues: {
            ':state': busyState,
            ':stateStart': timestamp,
            ':history': updatedHistory,
            ':updatedAt': new Date().toISOString()
        }
    }).promise();
    
    // Update interval metrics
    await updateIntervalMetrics(tenantId, agentId, session.currentState, previousStateDuration, timestamp);
    
    return { 
        success: true, 
        eventType: 'BusiedOut', 
        agentId, 
        busyState,
        action: 'state_changed' 
    };
}

async function getAgentSession(tenantId, agentId) {
    try {
        const result = await dynamodb.get({
            TableName: AGENT_SESSIONS_TABLE,
            Key: { tenantId, agentId }
        }).promise();
        
        return result.Item;
    } catch (error) {
        console.error('Error getting agent session:', error);
        return null;
    }
}

async function endExistingSession(tenantId, agentId, timestamp) {
    const existingSession = await getAgentSession(tenantId, agentId);
    
    if (existingSession && !existingSession.sessionEnd) {
        // Force logout of existing session
        await processLogout(tenantId, agentId, {}, timestamp);
    }
}

function getBusyStateType(action) {
    switch (action) {
        case 'WrapUpFixedDuration':
        case 'WrapUpVariableDuration':
            return 'BUSY_WRAPUP';
        case 'Break':
        case 'Training':
        case 'Manual':
            return 'BUSY_OTHER';
        default:
            return 'BUSY_OTHER';
    }
}

async function upsertAgentDimension(tenantId, agentId, parsedEvent) {
    const sql = `
        CALL reporting.upsert_agent(
            ${tenantId},
            '${agentId}',
            '${parsedEvent.role || ''}',
            '${parsedEvent.agentUri || ''}',
            '${parsedEvent.operatorId || ''}',
            '${parsedEvent.workstation || ''}'
        );
    `;
    
    try {
        await redshift.executeStatement({
            ClusterIdentifier: REDSHIFT_CLUSTER,
            Database: REDSHIFT_DATABASE,
            Sql: sql
        }).promise();
    } catch (error) {
        console.error('Error upserting agent dimension:', error);
    }
}

async function createAgentSessionFact(session, endTime, duration) {
    // Implementation for creating fact_agent_session record
    console.log('Creating agent session fact:', session.agentId, duration);
}

async function createAgentStateFacts(session, stateHistory) {
    // Implementation for creating fact_agent_state records
    console.log('Creating agent state facts:', session.agentId, stateHistory.length);
}

async function updateAgentIntervals(session, timestamp) {
    // Implementation for updating fact_agent_intervals
    console.log('Updating agent intervals:', session.agentId);
}

async function updateIntervalMetrics(tenantId, agentId, previousState, duration, timestamp) {
    // Update 5-minute interval buckets
    const intervalStart = new Date(timestamp);
    intervalStart.setMinutes(Math.floor(intervalStart.getMinutes() / 5) * 5, 0, 0);
    
    console.log(`Updating interval metrics for ${agentId}: ${previousState} for ${duration}s`);
}
