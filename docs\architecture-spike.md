# ACD/Agent Reporting Architecture Spike
## Two Approaches: Collector API vs AWS Native Processing

This spike documents two serverless architectures for ACD (Automatic Call Distribution) and Agent reporting using AWS Lambda and Redshift Serverless. Both approaches process call data and real-time agent events to populate an optimized star schema data warehouse with SCD Type 2 (Slowly Changing Dimensions) support.

**Key Innovation**: Tenant-specific Lambda functions with SNS filtering for optimal isolation and performance, plus 15-minute interval pre-aggregation for near real-time dashboard performance.

## Architecture Decision: Two Approaches

### Approach 1: Hybrid (Collector API + AWS Native ACD)
- **Call Processing**: Existing Collector API → MariaDB callsummary → Redshift ETL
- **Agent Processing**: AWS Native Lambda → Direct Redshift fact tables
- **Best For**: Minimal disruption to existing call processing infrastructure

### Approach 2: Full AWS Native
- **Call Processing**: AWS Lambda → Direct Redshift fact tables (no Collector API)
- **Agent Processing**: AWS Lambda → Direct Redshift fact tables  
- **Best For**: Complete serverless architecture with maximum scalability

## Business Requirements

### Target Reports
1. **ACD - Detailed Calls by Group**: Agent performance metrics by queue with staffed time, available time, wrap-up time
2. **ACD - Call Queue Summary Report**: Queue performance dashboard with group utilization percentages
3. **ACD - Call Taking Group Overview**: Service level analysis with detailed time threshold breakdowns

### Key Metrics Required
- **Call Volume**: Calls answered, transferred, abandoned per queue/agent
- **Service Levels**: Percentage of calls answered within 10s, 15s, 20s, 40s thresholds
- **Agent Utilization**: Talk time vs logged-in time ratios (Group Utilization %)
- **Staffed Time**: Total time agents were logged into queues (from Login/Logout events)
- **Available Time**: Time agents were available to take calls (from Available/BusiedOut events)
- **Wrap-up Time**: Time spent in post-call processing
- **Agent Context**: Agent roles, workstations, operator IDs, reason codes

### Performance Requirements
- **Scale**: Handle hundreds of events per second per tenant
- **Latency**: Near real-time reporting (5-minute refresh cycles)
- **Multi-tenant**: Complete client isolation with tenant-specific Lambda functions
- **Historical Accuracy**: SCD Type 2 for tracking agent role changes over time

## Tenant-Specific Architecture Pattern

### SNS Topic Filtering Strategy
Instead of using SQS queues, we use SNS message filtering to route events directly to tenant-specific Lambda functions:

```json
{
  "tenant_id": ["brandon911"],
  "event_category": ["call", "agent", "queue"]
}
```

### Benefits of Tenant-Specific Lambdas
1. **Complete Isolation**: Each tenant has dedicated processing resources
2. **Independent Scaling**: Tenant-specific auto-scaling based on their event volume
3. **Simplified Configuration**: Tenant-specific environment variables and settings
4. **Cost Attribution**: Clear cost allocation per tenant
5. **Fault Isolation**: Issues with one tenant don't affect others

## Approach 1: Hybrid Architecture (Collector API + AWS Native)

### High-Level Flow
```
S3 i3 Logs → SNS → [Tenant Filter] → Tenant-Specific Lambdas
                                   ├── Call Events → Collector API → MariaDB → Redshift ETL
                                   └── Agent Events → Direct Redshift Processing
```

### Detailed Architecture

#### Call Processing Pipeline (Existing)
```
S3 → SNS → Lambda(tenant-call-processor) → Collector API (EC2) → MariaDB callsummary → Redshift ETL
```

**Components**:
- **SNS Filter**: `{"tenant_id": ["brandon911"], "event_type": ["StartCall", "EndCall", "Answer"]}`
- **Lambda**: `brandon911-call-processor` (existing logic, minimal changes)
- **Collector API**: Existing EC2 infrastructure (unchanged)
- **MariaDB**: Existing callsummary table structure
- **Redshift ETL**: New scheduled job to transform callsummary → fact_call

#### Agent Processing Pipeline (New AWS Native)
```
S3 → SNS → Lambda(tenant-agent-processor) → Redshift (Direct Insert)
```

**Components**:
- **SNS Filter**: `{"tenant_id": ["brandon911"], "event_type": ["Login", "Logout", "Available", "BusiedOut"]}`
- **Lambda**: `brandon911-agent-processor` (new serverless function)
- **DynamoDB**: Tenant-specific agent session tracking
- **Redshift**: Direct insert to fact_agent_session, fact_agent_state tables

### Approach 1 Benefits
- **Low Risk**: Existing call processing remains unchanged
- **Incremental Migration**: Can migrate tenants one at a time
- **Proven Reliability**: Collector API is battle-tested
- **Quick Implementation**: Only agent processing needs to be built

### Approach 1 Limitations
- **Hybrid Complexity**: Two different processing paradigms
- **EC2 Dependency**: Still requires EC2 infrastructure for calls
- **Scaling Constraints**: Collector API may become bottleneck
- **Cost**: EC2 instances running 24/7

## Approach 2: Full AWS Native Architecture

### High-Level Flow
```
S3 i3 Logs → SNS → [Tenant Filter] → Tenant-Specific Lambdas → DynamoDB State → Redshift Facts
                                   ├── Call Events → Call Completion Logic → fact_call
                                   └── Agent Events → Session Tracking → fact_agent_*
```

### Detailed Architecture

#### Call Processing Pipeline (New AWS Native)
```
S3 → SNS → Lambda(tenant-call-processor) → DynamoDB(active-calls) → Step Functions → Redshift
```

**Components**:
- **SNS Filter**: `{"tenant_id": ["brandon911"], "event_type": ["StartCall", "EndCall", "CDRType1"]}`
- **Lambda**: `brandon911-call-processor` (new serverless function)
- **DynamoDB**: `brandon911-active-calls` table for call state tracking
- **Step Functions**: Call completion orchestration
- **Redshift**: Direct insert to fact_call table

#### Agent Processing Pipeline (AWS Native)
```
S3 → SNS → Lambda(tenant-agent-processor) → DynamoDB(agent-sessions) → Redshift
```

**Components**:
- **SNS Filter**: `{"tenant_id": ["brandon911"], "event_type": ["Login", "Logout", "Available", "BusiedOut"]}`
- **Lambda**: `brandon911-agent-processor` (new serverless function)
- **DynamoDB**: `brandon911-agent-sessions` table for session tracking
- **Redshift**: Direct insert to fact_agent_session, fact_agent_state tables

### Call Completion Logic (AWS Native)
Based on your existing ER diagram requirements:

```javascript
// Call completion criteria
function isCallComplete(callEvents) {
    const hasEndCall = callEvents.some(e => e.type === 'EndCall');
    const hasEndMedia = callEvents.some(e => e.type === 'EndMedia');
    const hasCDRType1 = callEvents.some(e => e.type === 'CDRType1');
    const isOutbound = callEvents.some(e => e.type === 'OutboundCall');
    
    // Must have EndCall OR EndMedia
    const hasEndEvent = hasEndCall || hasEndMedia;
    
    if (!hasEndEvent) return false;
    
    // Outbound calls don't require CDRType1
    if (isOutbound) return true;
    
    // Emergency/Admin calls require CDRType1
    return hasCDRType1;
}
```

### Approach 2 Benefits
- **Full Serverless**: No EC2 infrastructure required
- **Maximum Scalability**: Auto-scales to handle any event volume
- **Cost Efficiency**: Pay only for actual processing time
- **Unified Architecture**: Single processing paradigm for all events
- **Real-time Processing**: Sub-second event processing

### Approach 2 Challenges
- **Higher Complexity**: Need to implement call completion logic
- **Migration Risk**: Requires replacing proven call processing
- **Development Time**: More Lambda functions to build and test
- **State Management**: Complex DynamoDB state tracking required

## Tenant-Specific Lambda Implementation

### SNS Topic Structure
```
arn:aws:sns:region:account:i3-events-brandon911
arn:aws:sns:region:account:i3-events-client2
arn:aws:sns:region:account:i3-events-client3
```

### Lambda Function Naming Convention
```
brandon911-call-processor
brandon911-agent-processor
brandon911-data-transformer
brandon911-interval-aggregator

client2-call-processor
client2-agent-processor
...
```

### SNS Subscription Filters
```json
{
  "tenant_id": ["brandon911"],
  "event_category": ["call"]
}

{
  "tenant_id": ["brandon911"], 
  "event_category": ["agent"]
}
```

### Environment Variables (Per Tenant)
```javascript
// brandon911-call-processor environment
{
  "TENANT_ID": "brandon911",
  "REDSHIFT_SCHEMA": "brandon911_reporting",
  "DYNAMODB_TABLE_PREFIX": "brandon911",
  "COLLECTOR_API_ENDPOINT": "https://brandon911-collector.example.com"
}
```

## Data Warehouse Schema (Both Approaches)

### Star Schema Design
**Dimensions (SCD Type 2)**:
- `dim_tenant` - Multi-client isolation
- `dim_agent` - Agent master with roles, workstations, operator IDs  
- `dim_queue` - Queue master with ring group names
- `dim_date` - Date dimension for temporal analysis
- `dim_time15` - 15-minute time buckets (96 per day)
- `dim_call_type` - Emergency/Admin/Outbound classification
- `dim_reason_code` - Agent unavailability reasons

**Facts (Transactional Data)**:
- `fact_call` - Individual call records with service level flags
- `fact_agent_session` - Login/logout sessions per queue
- `fact_agent_state` - State changes (Available/Busy/Wrap-up/Break)
- `fact_agent_interval15` - Pre-aggregated 15-minute agent metrics
- `fact_queue_interval15` - Pre-aggregated 15-minute queue metrics

### Tenant Isolation in Redshift
```sql
-- Option 1: Schema per tenant
CREATE SCHEMA brandon911_reporting;
CREATE SCHEMA client2_reporting;

-- Option 2: Tenant column with RLS
CREATE TABLE reporting.fact_call (
    tenant_id VARCHAR(50) NOT NULL,
    call_id BIGINT,
    ...
) DISTKEY(tenant_id);
```

## Implementation Comparison

| Aspect | Approach 1 (Hybrid) | Approach 2 (AWS Native) |
|--------|---------------------|-------------------------|
| **Development Time** | 2-3 weeks | 4-6 weeks |
| **Risk Level** | Low | Medium |
| **Scalability** | Limited by EC2 | Unlimited |
| **Cost (per tenant)** | $200-500/month | $50-200/month |
| **Maintenance** | EC2 + Lambda | Lambda only |
| **Real-time Capability** | Good | Excellent |
| **Migration Complexity** | Low | High |

## Recommendation

### Phase 1: Start with Approach 1 (Hybrid)
- **Rationale**: Lower risk, faster implementation, proven call processing
- **Timeline**: 2-3 weeks for first tenant
- **Scope**: Implement agent processing only, keep existing call processing

### Phase 2: Migrate to Approach 2 (Full AWS Native)  
- **Rationale**: After proving agent processing works, migrate call processing
- **Timeline**: Additional 3-4 weeks
- **Scope**: Replace Collector API with AWS native call processing

### Implementation Priority
1. **Week 1-2**: Tenant-specific SNS filtering and Lambda infrastructure
2. **Week 3**: Agent processing Lambda (Login/Logout/Available/BusiedOut)
3. **Week 4**: Redshift schema and ETL for agent data
4. **Week 5**: Power BI integration and testing
5. **Week 6+**: Optional migration to full AWS native call processing

This phased approach minimizes risk while providing a clear path to full serverless architecture.

## Tenant-Specific SNS Filtering Implementation

### SNS Message Attributes for Filtering
When events are published to SNS, they include message attributes for filtering:

```javascript
// Event Router Lambda publishes with attributes
const snsParams = {
    TopicArn: `arn:aws:sns:region:account:i3-events-${tenantId}`,
    Message: JSON.stringify(eventData),
    MessageAttributes: {
        tenant_id: {
            DataType: 'String',
            StringValue: tenantId
        },
        event_type: {
            DataType: 'String',
            StringValue: eventType
        },
        event_category: {
            DataType: 'String',
            StringValue: getEventCategory(eventType) // 'call', 'agent', 'queue'
        }
    }
};
```

### SNS Subscription Filters per Lambda
```json
// Call processor subscription filter
{
  "tenant_id": ["brandon911"],
  "event_category": ["call"]
}

// Agent processor subscription filter
{
  "tenant_id": ["brandon911"],
  "event_category": ["agent"]
}

// Queue processor subscription filter
{
  "tenant_id": ["brandon911"],
  "event_category": ["queue"]
}
```

### Benefits of SNS Filtering vs SQS Queues
1. **Direct Routing**: Events go directly to target Lambda, no intermediate queues
2. **Reduced Latency**: Eliminates SQS polling delay
3. **Cost Reduction**: No SQS message charges
4. **Simplified Architecture**: Fewer moving parts
5. **Automatic Scaling**: SNS handles fan-out automatically
6. **Built-in Retry**: SNS has built-in retry and DLQ support

## Per-Tenant Resource Isolation

### Naming Convention
```
# Lambda Functions
{tenant-id}-call-processor
{tenant-id}-agent-processor
{tenant-id}-queue-processor
{tenant-id}-data-transformer
{tenant-id}-interval-aggregator

# DynamoDB Tables
{tenant-id}-active-calls
{tenant-id}-agent-sessions
{tenant-id}-queue-states

# SNS Topics
i3-events-{tenant-id}

# Step Functions
{tenant-id}-call-completion
{tenant-id}-agent-session-workflow

# Redshift Schemas
{tenant-id}_reporting
```

### Environment Variables per Tenant
```javascript
// brandon911-call-processor environment
{
  "TENANT_ID": "brandon911",
  "REDSHIFT_SCHEMA": "brandon911_reporting",
  "ACTIVE_CALLS_TABLE": "brandon911-active-calls",
  "COLLECTOR_API_ENDPOINT": "https://brandon911-collector.example.com" // Approach 1 only
}

// client2-call-processor environment
{
  "TENANT_ID": "client2",
  "REDSHIFT_SCHEMA": "client2_reporting",
  "ACTIVE_CALLS_TABLE": "client2-active-calls",
  "COLLECTOR_API_ENDPOINT": "https://client2-collector.example.com" // Approach 1 only
}
```

## Implementation Roadmap

### Phase 1: Infrastructure Setup (Week 1)
1. **SNS Topic Creation**: Create tenant-specific SNS topics
2. **Lambda Scaffolding**: Create basic Lambda functions with tenant isolation
3. **DynamoDB Tables**: Create tenant-specific state tables
4. **IAM Roles**: Set up least-privilege access per tenant
5. **CloudFormation**: Parameterized templates for tenant deployment

### Phase 2: Approach 1 Implementation (Week 2-3)
1. **Call Processor**: Minimal Lambda that forwards to Collector API
2. **Agent Processor**: Full AWS native agent event processing
3. **Redshift Schema**: Create tenant-specific reporting schema
4. **ETL Jobs**: Transform callsummary to fact_call tables
5. **Testing**: End-to-end testing with sample events

### Phase 3: Power BI Integration (Week 4)
1. **Redshift Views**: Create optimized views for Power BI
2. **Service Level Calculations**: Implement all required metrics
3. **Report Templates**: Create Power BI report templates
4. **Performance Tuning**: Optimize queries and indexes
5. **User Acceptance Testing**: Validate reports with stakeholders

### Phase 4: Production Deployment (Week 5)
1. **Monitoring**: CloudWatch dashboards and alerts
2. **Error Handling**: Dead letter queues and error notifications
3. **Documentation**: Operational runbooks and troubleshooting guides
4. **Training**: User training on new reports and dashboards
5. **Go-Live**: Production deployment with rollback plan

### Phase 5: Optional Migration to Approach 2 (Week 6+)
1. **Call Completion Logic**: Implement AWS native call processing
2. **Step Functions**: Create call completion workflows
3. **Migration Testing**: Parallel processing to validate accuracy
4. **Gradual Cutover**: Migrate tenants one by one
5. **Decommission**: Remove Collector API infrastructure

This roadmap provides a clear path from current state to full serverless architecture with minimal risk and maximum tenant isolation.
