const AWS = require('aws-sdk');

const dynamodb = new AWS.DynamoDB.DocumentClient();
const stepfunctions = new AWS.StepFunctions();

const ACTIVE_CALLS_TABLE = process.env.ACTIVE_CALLS_TABLE;
const CALL_COMPLETION_STATE_MACHINE = process.env.CALL_COMPLETION_STATE_MACHINE_ARN;

exports.handler = async (event) => {
    console.log('Call Event Processor received:', JSON.stringify(event, null, 2));
    
    const results = [];
    
    for (const record of event.Records) {
        try {
            const message = JSON.parse(record.body);
            const result = await processCallEvent(message);
            results.push(result);
        } catch (error) {
            console.error('Error processing call event:', error);
            results.push({ success: false, error: error.message });
        }
    }
    
    return {
        statusCode: 200,
        body: JSON.stringify({
            processed: results.length,
            results: results
        })
    };
};

async function processCallEvent(message) {
    const { tenantId, eventType, callIdentifier, parsedEvent, timestamp } = message;
    
    console.log(`Processing ${eventType} for call ${callIdentifier}, tenant ${tenantId}`);
    
    // Get or create call record
    let callRecord = await getCallRecord(tenantId, callIdentifier);
    
    if (!callRecord) {
        callRecord = await createCallRecord(tenantId, callIdentifier, parsedEvent);
    }
    
    // Add event to call timeline
    await addEventToCall(callRecord, eventType, parsedEvent, timestamp);
    
    // Check if call is complete
    const isComplete = await checkCallCompletion(callRecord);
    
    if (isComplete) {
        console.log(`Call ${callIdentifier} is complete, triggering Step Function`);
        await triggerCallCompletion(callRecord);
    }
    
    return { 
        success: true, 
        callIdentifier, 
        eventType, 
        isComplete,
        eventCount: callRecord.events.length 
    };
}

async function getCallRecord(tenantId, callIdentifier) {
    const params = {
        TableName: ACTIVE_CALLS_TABLE,
        Key: {
            tenantId: tenantId,
            callIdentifier: callIdentifier
        }
    };
    
    try {
        const result = await dynamodb.get(params).promise();
        return result.Item;
    } catch (error) {
        console.error('Error getting call record:', error);
        return null;
    }
}

async function createCallRecord(tenantId, callIdentifier, parsedEvent) {
    const callRecord = {
        tenantId: tenantId,
        callIdentifier: callIdentifier,
        status: 'ACTIVE',
        events: [],
        startTime: parsedEvent.timestamp,
        agencyOrElement: parsedEvent.agencyOrElement,
        agent: parsedEvent.agent,
        createdAt: new Date().toISOString(),
        ttl: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours TTL
        completionFlags: {
            hasStartCall: false,
            hasEndCall: false,
            hasEndMedia: false,
            hasCDRType1: false,
            hasOutboundCall: false
        }
    };
    
    const params = {
        TableName: ACTIVE_CALLS_TABLE,
        Item: callRecord
    };
    
    await dynamodb.put(params).promise();
    return callRecord;
}

async function addEventToCall(callRecord, eventType, parsedEvent, timestamp) {
    const eventData = {
        eventType: eventType,
        timestamp: timestamp,
        data: parsedEvent,
        receivedAt: new Date().toISOString()
    };
    
    // Update completion flags
    const updateExpression = [];
    const expressionAttributeValues = {};
    
    updateExpression.push('events = list_append(if_not_exists(events, :empty_list), :new_event)');
    expressionAttributeValues[':empty_list'] = [];
    expressionAttributeValues[':new_event'] = [eventData];
    
    // Update completion flags based on event type
    switch (eventType) {
        case 'StartCall':
            updateExpression.push('completionFlags.hasStartCall = :true');
            expressionAttributeValues[':true'] = true;
            break;
        case 'EndCall':
            updateExpression.push('completionFlags.hasEndCall = :true');
            break;
        case 'EndMedia':
            updateExpression.push('completionFlags.hasEndMedia = :true');
            break;
        case 'CDRtype1':
            updateExpression.push('completionFlags.hasCDRType1 = :true');
            break;
        case 'OutboundCall':
            updateExpression.push('completionFlags.hasOutboundCall = :true');
            break;
    }
    
    const params = {
        TableName: ACTIVE_CALLS_TABLE,
        Key: {
            tenantId: callRecord.tenantId,
            callIdentifier: callRecord.callIdentifier
        },
        UpdateExpression: 'SET ' + updateExpression.join(', '),
        ExpressionAttributeValues: expressionAttributeValues
    };
    
    await dynamodb.update(params).promise();
}

async function checkCallCompletion(callRecord) {
    const flags = callRecord.completionFlags;
    
    // Completion criteria based on your existing logic:
    // 1. Must have EndCall OR EndMedia
    // 2. For non-outbound calls, must also have CDRType1
    
    const hasEndEvent = flags.hasEndCall || flags.hasEndMedia;
    
    if (!hasEndEvent) {
        return false;
    }
    
    // Outbound calls don't require CDRType1
    if (flags.hasOutboundCall) {
        return true;
    }
    
    // Emergency/Admin calls require CDRType1
    return flags.hasCDRType1;
}

async function triggerCallCompletion(callRecord) {
    const input = {
        tenantId: callRecord.tenantId,
        callIdentifier: callRecord.callIdentifier,
        completedAt: new Date().toISOString()
    };
    
    const params = {
        stateMachineArn: CALL_COMPLETION_STATE_MACHINE,
        input: JSON.stringify(input),
        name: `call-completion-${callRecord.tenantId}-${callRecord.callIdentifier}-${Date.now()}`
    };
    
    try {
        const result = await stepfunctions.startExecution(params).promise();
        console.log('Started Step Function execution:', result.executionArn);
        
        // Mark call as processing
        await dynamodb.update({
            TableName: ACTIVE_CALLS_TABLE,
            Key: {
                tenantId: callRecord.tenantId,
                callIdentifier: callRecord.callIdentifier
            },
            UpdateExpression: 'SET #status = :status, stepFunctionArn = :arn',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': 'PROCESSING',
                ':arn': result.executionArn
            }
        }).promise();
        
        return result;
    } catch (error) {
        console.error('Error starting Step Function:', error);
        throw error;
    }
}
